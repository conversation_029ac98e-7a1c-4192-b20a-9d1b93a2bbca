# IR3 V2 Key Features 组件布局调整指南

## 概述
本指南详细说明如何调整 IR3 V2 Key Features 组件中各元素的间距和位置，特别是针对桌面端和移动端的不同需求。

## 主要调整元素

### 1. Title Group（标题组）
**文件位置**: `assets/ir3-v2-key-features.css` 第 139-146 行

```css
/* Title Group */
.key-features-section .title-group {
  text-align: center;
  margin-bottom: 15px;    /* 控制标题组与下方元素的距离 */
  margin-top: -30px;      /* 负值向上偏移，正值向下偏移 */
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}
```

**调整说明**:
- `margin-top`: 控制标题组的垂直位置
  - 负值（如 -30px）：向上偏移
  - 正值（如 20px）：向下偏移
  - 建议范围：-50px 到 30px
- `margin-bottom`: 控制与下方 feature-tags 的距离
  - 建议范围：10px 到 25px

### 2. Feature Tags（功能标签）
**文件位置**: `assets/ir3-v2-key-features.css` 第 561-570 行

```css
.key-features-section .feature-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;              /* 标签之间的间距 */
  margin-bottom: 30px;    /* 与下方内容的距离 */
  margin-top: -10px;      /* 与上方标题组的距离 */
  position: relative;
  z-index: 25;
}
```

**调整说明**:
- `margin-top`: 控制与标题组的距离
  - 负值：向上靠近标题
  - 正值：远离标题
  - 建议范围：-20px 到 20px
- `margin-bottom`: 控制与下方功能展示区的距离
  - 建议范围：20px 到 40px
- `gap`: 控制标签按钮之间的间距
  - 建议范围：15px 到 25px

### 3. Feature Showcase（功能展示区）
**文件位置**: `assets/ir3-v2-key-features.css` 第 231-241 行

```css
/* Feature Showcase */
.key-features-section .feature-showcase {
  display: grid;
  grid-template-columns: 80px 1fr 80px;
  align-items: center;
  gap: 30px;
  margin-bottom: 20px;    /* 与底部的距离 */
  position: relative;
  z-index: 5;
}
```

## 响应式调整

### 移动端（768px 以下）
**文件位置**: `assets/ir3-v2-key-features.css` 第 667-670 行

```css
.key-features-section .title-group {
  margin-bottom: 30px;    /* 移动端保持较大间距 */
  /* 移动端不使用负 margin-top，保持 0 或正值 */
}
```

**文件位置**: `assets/ir3-v2-key-features.css` 第 695-699 行

```css
.key-features-section .feature-tags {
  gap: 12px;              /* 移动端减少间距 */
  margin-bottom: 25px;
  margin-top: 10px;       /* 移动端使用正值 */
}
```

## 常用调整场景

### 场景1：标题太靠下，需要向上移动
```css
.key-features-section .title-group {
  margin-top: -40px;  /* 增加负值 */
}
```

### 场景2：标签与标题距离太远
```css
.key-features-section .feature-tags {
  margin-top: -15px;  /* 增加负值 */
}
```

### 场景3：整体内容太紧凑
```css
.key-features-section .title-group {
  margin-bottom: 20px;  /* 增加底部间距 */
}
.key-features-section .feature-tags {
  margin-bottom: 35px;  /* 增加底部间距 */
}
```

### 场景4：移动端布局调整
```css
@media screen and (max-width: 768px) {
  .key-features-section .title-group {
    margin-top: 0;        /* 移动端不使用负值 */
    margin-bottom: 25px;
  }
  
  .key-features-section .feature-tags {
    margin-top: 5px;      /* 移动端使用小正值 */
    margin-bottom: 20px;
  }
}
```

## 调整步骤

1. **确定调整目标**：明确要调整哪个元素的位置
2. **找到对应CSS规则**：使用本指南中的行号定位
3. **小幅度调整**：每次调整 5-10px，避免大幅改动
4. **测试不同设备**：确保桌面端和移动端都正常显示
5. **保存并预览**：在浏览器中查看效果

## 注意事项

- **桌面端可以使用负 margin**：用于精确控制位置
- **移动端建议使用正 margin**：避免内容溢出或重叠
- **保持响应式兼容**：确保不同断点下的布局都合理
- **测试边界情况**：检查极端内容长度下的显示效果

## 快速参考

| 元素 | 向上移动 | 向下移动 | 增加间距 | 减少间距 |
|------|----------|----------|----------|----------|
| Title Group | margin-top: -30px | margin-top: 10px | margin-bottom: 25px | margin-bottom: 10px |
| Feature Tags | margin-top: -15px | margin-top: 15px | margin-bottom: 35px | margin-bottom: 20px |
| Feature Showcase | 调整上方元素 | 调整上方元素 | margin-bottom: 30px | margin-bottom: 10px |

## 实际修改示例

### 当前推荐设置（桌面端）
```css
/* 标题组 - 向上偏移30px */
.key-features-section .title-group {
  margin-top: -30px;
  margin-bottom: 15px;
}

/* 功能标签 - 向上偏移10px */
.key-features-section .feature-tags {
  margin-top: -10px;
  margin-bottom: 30px;
}
```

### 移动端兼容设置
```css
@media screen and (max-width: 768px) {
  .key-features-section .title-group {
    margin-top: 0;        /* 移动端重置为0 */
    margin-bottom: 30px;
  }

  .key-features-section .feature-tags {
    margin-top: 10px;     /* 移动端使用正值 */
    margin-bottom: 25px;
  }
}
```

## 故障排除

### 问题1：标签排版错位
**症状**：feature-tags 显示位置不正确
**解决方案**：
```css
.key-features-section .feature-tags {
  margin-top: -10px;    /* 调整为负值 */
  margin-bottom: 30px;
}
```

### 问题2：移动端内容重叠
**症状**：移动设备上元素重叠
**解决方案**：在移动端媒体查询中重置负边距
```css
@media screen and (max-width: 768px) {
  .key-features-section .title-group {
    margin-top: 0 !important;
  }
}
```

### 问题3：整体布局太紧凑
**症状**：元素之间距离太近
**解决方案**：增加各元素的 margin-bottom
```css
.key-features-section .title-group {
  margin-bottom: 20px;  /* 增加到20px */
}
.key-features-section .feature-tags {
  margin-bottom: 35px;  /* 增加到35px */
}
```
