// IR3 Video Scroll Component JavaScript
class IR3VideoScroll {
  constructor(sectionId) {
    this.sectionId = sectionId;
    this.section = document.querySelector(`#ir3-video-scroll-${sectionId}`);
    this.video = null;
    this.contentWrapper = null;
    this.placeholder = null;
    this.loadingElement = null;

    // 懒加载状态管理
    this.lazyState = 'idle'; // idle, loading, loaded, playing, error
    this.isVideoLoaded = false;
    this.isIntersecting = false;
    this.loadAttempts = 0;
    this.maxLoadAttempts = 3;

    // ScrollTrigger和Observer实例
    this.scrollTriggerInstance = null;
    this.contentAnimationInstance = null;
    this.intersectionObserver = null;

    if (!this.section) {
      console.warn(`IR3 Video Scroll: Section with ID ${sectionId} not found`);
      return;
    }

    this.init();
  }

  init() {
    this.setupElements();
    this.checkGSAP();
    this.setupLazyLoading(); // 设置懒加载
    this.bindEvents();

    console.log(`IR3 Video Scroll component initialized for section: ${this.sectionId}`);
  }

  setupElements() {
    this.video = this.section.querySelector('.background-video');
    this.contentWrapper = this.section.querySelector('.content-wrapper');
    this.placeholder = this.section.querySelector('.video-placeholder');
    this.loadingElement = this.section.querySelector('.video-loading');

    if (!this.video || !this.contentWrapper) {
      console.warn('IR3 Video Scroll: Required elements not found');
      return;
    }

    // 初始化懒加载状态
    this.setLazyState('idle');
  }

  checkGSAP() {
    if (typeof gsap === 'undefined') {
      console.warn('GSAP not loaded for IR3 Video Scroll');
      this.loadGSAPFallback();
      return false;
    }
    
    if (typeof ScrollTrigger === 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
    }
    
    return true;
  }

  loadGSAPFallback() {
    // 简单的fallback动画，不依赖GSAP
    this.contentWrapper.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.contentWrapper.style.opacity = '1';
          this.contentWrapper.style.transform = 'translateY(0)';
        }
      });
    }, { threshold: 0.3 });
    
    observer.observe(this.section);
  }

  // 懒加载状态管理方法
  setLazyState(state) {
    this.lazyState = state;
    this.section.setAttribute('data-lazy-state', state);

    console.log(`� 懒加载状态变更: ${state}`);

    // 根据状态更新UI
    switch (state) {
      case 'idle':
        this.showPlaceholder();
        break;
      case 'loading':
        this.showLoading();
        break;
      case 'loaded':
        this.hideLoading();
        this.hidePlaceholder();
        break;
      case 'playing':
        this.hideLoading();
        this.hidePlaceholder();
        break;
      case 'error':
        this.showError();
        break;
    }
  }

  showPlaceholder() {
    if (this.placeholder) {
      this.placeholder.classList.remove('hidden');
    }
  }

  hidePlaceholder() {
    if (this.placeholder) {
      this.placeholder.classList.add('hidden');
    }
  }

  showLoading() {
    if (this.loadingElement) {
      this.loadingElement.classList.add('visible');
    }
    this.hidePlaceholder();
  }

  hideLoading() {
    if (this.loadingElement) {
      this.loadingElement.classList.remove('visible');
    }
  }

  showError() {
    this.hideLoading();
    if (this.placeholder) {
      // 隐藏spinner，显示错误状态
      const spinner = this.placeholder.querySelector('.placeholder-spinner');
      const errorText = this.placeholder.querySelector('.placeholder-text');

      if (spinner) {
        spinner.style.display = 'none';
      }

      if (errorText) {
        errorText.textContent = 'Failed to load video. Click to retry.';
        errorText.style.color = 'rgba(255, 255, 255, 0.9)';
        this.placeholder.style.cursor = 'pointer';
        this.placeholder.addEventListener('click', () => this.retryLoad());
      }
    }
  }

  // 设置懒加载
  setupLazyLoading() {
    console.log('🔍 设置懒加载 IntersectionObserver');

    // 创建懒加载观察器
    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        this.isIntersecting = entry.isIntersecting;

        if (entry.isIntersecting && this.lazyState === 'idle') {
          console.log('✅ 组件进入视口，开始加载视频');
          this.startVideoLoad();
        } else if (!entry.isIntersecting && this.lazyState === 'playing') {
          console.log('⏸️ 组件离开视口，暂停视频');
          this.pauseVideo();
        }
      });
    }, {
      rootMargin: '200px', // 提前200px开始加载
      threshold: 0.1
    });

    this.intersectionObserver.observe(this.section);
  }

  setupIntersectionObserver() {
    // 这个方法现在由setupLazyLoading替代
    console.log('📝 setupIntersectionObserver 已被懒加载替代');
  }

  // 开始视频加载
  startVideoLoad() {
    if (this.lazyState !== 'idle' || !this.video) return;

    this.setLazyState('loading');
    this.loadAttempts++;

    console.log(`🎬 开始加载视频 (尝试 ${this.loadAttempts}/${this.maxLoadAttempts})`);

    // 设置视频源并开始加载
    const videoSrc = this.video.dataset.videoSrc;
    if (videoSrc) {
      this.video.src = videoSrc;
      this.initVideo();
    } else {
      console.error('视频源未找到');
      this.setLazyState('error');
    }
  }

  // 重试加载
  retryLoad() {
    if (this.loadAttempts >= this.maxLoadAttempts) {
      console.warn('已达到最大重试次数');
      return;
    }

    console.log('🔄 重试加载视频');

    // 恢复spinner显示
    if (this.placeholder) {
      const spinner = this.placeholder.querySelector('.placeholder-spinner');
      const errorText = this.placeholder.querySelector('.placeholder-text');

      if (spinner) {
        spinner.style.display = 'block';
      }

      if (errorText) {
        errorText.textContent = 'Loading video...';
        errorText.style.color = 'rgba(255, 255, 255, 0.9)';
      }

      this.placeholder.style.cursor = 'default';
    }

    this.setLazyState('idle');
    setTimeout(() => this.startVideoLoad(), 1000);
  }

  initVideo() {
    if (!this.video) return;

    // 优化视频属性
    this.video.preload = 'auto';
    this.video.playsInline = true;
    this.video.muted = true;
    this.video.loop = true;

    // 添加性能优化属性
    this.video.style.willChange = 'transform';
    this.video.style.backfaceVisibility = 'hidden';
    this.video.style.perspective = '1000px';

    // 重置视频状态
    this.video.currentTime = 0;
    this.video.pause();

    // 添加加载事件监听器
    this.video.addEventListener('loadedmetadata', () => {
      console.log('✅ 视频元数据加载完成');
      this.isVideoLoaded = true;
      this.setLazyState('loaded');
      this.onVideoLoaded();
    });

    this.video.addEventListener('canplaythrough', () => {
      console.log('✅ 视频可以流畅播放');
      if (this.isIntersecting) {
        this.playVideo();
      }
    });

    this.video.addEventListener('error', (e) => {
      console.error('❌ 视频加载错误:', e);
      this.handleVideoError();
    });

    // 开始加载视频
    this.video.load();
  }

  // 优化视频播放性能
  optimizeVideoPlayback() {
    if (!this.video) return;

    // 设置视频循环播放
    this.video.loop = true;

    // 设置视频解码优化
    if ('requestVideoFrameCallback' in this.video) {
      this.video.requestVideoFrameCallback(() => {
        console.log('Video frame callback available - using optimized rendering');
      });
    }

    // 预缓冲视频
    this.video.currentTime = 0.1;
    this.video.currentTime = 0;
  }

  onVideoLoaded() {
    console.log(`✅ 视频加载成功. 时长: ${this.video.duration}s`);

    // 创建动画
    this.createAnimations();

    // 如果组件在视口内，开始播放
    if (this.isIntersecting) {
      this.playVideo();
    }
  }

  handleVideoError() {
    console.error('❌ 视频加载失败');

    if (this.loadAttempts < this.maxLoadAttempts) {
      console.log(`🔄 将在2秒后重试 (${this.loadAttempts}/${this.maxLoadAttempts})`);
      setTimeout(() => this.retryLoad(), 2000);
    } else {
      console.error('❌ 已达到最大重试次数，加载失败');
      this.setLazyState('error');
    }
  }

  // 播放视频
  playVideo() {
    if (!this.video || !this.isVideoLoaded) return;

    this.video.play().then(() => {
      console.log('▶️ 视频开始播放');
      this.setLazyState('playing');
    }).catch(e => {
      console.log('播放失败，尝试静音播放:', e);
      this.video.muted = true;
      this.video.play().catch(err => {
        console.error('静音播放也失败:', err);
      });
    });
  }

  // 暂停视频
  pauseVideo() {
    if (!this.video) return;

    this.video.pause();
    console.log('⏸️ 视频已暂停');
  }

  createAnimations() {
    console.log('🚀 createAnimations 开始执行');
    if (!this.checkGSAP()) {
      console.log('❌ GSAP 检查失败，动画创建中止');
      return;
    }

    console.log('✅ GSAP 检查通过，开始创建动画');
    this.createVideoScrollAnimation();
    this.createContentAnimation();
    this.createGradientMaskAnimation();
    this.setupScrollTriggerRefresh();
    console.log('🎉 所有动画创建完成');
  }

  createVideoScrollAnimation() {
    if (!this.video || !this.isVideoLoaded) return;

    console.log('🎬 创建视频滚动动画');

    // 由于我们已经有懒加载的IntersectionObserver，这里只需要简单的播放控制
    this.scrollTriggerInstance = ScrollTrigger.create({
      trigger: this.section,
      start: 'top 90%',
      end: 'bottom 10%',
      onEnter: () => {
        console.log('📍 ScrollTrigger: 进入视频区域');
        this.section.classList.add('video-active');
        if (this.lazyState === 'loaded' || this.lazyState === 'playing') {
          this.playVideo();
        }
      },
      onLeave: () => {
        console.log('📍 ScrollTrigger: 离开视频区域');
        this.section.classList.remove('video-active');
        this.pauseVideo();
      },
      onEnterBack: () => {
        console.log('📍 ScrollTrigger: 重新进入视频区域');
        this.section.classList.add('video-active');
        if (this.lazyState === 'loaded' || this.lazyState === 'playing') {
          this.playVideo();
        }
      },
      onLeaveBack: () => {
        console.log('📍 ScrollTrigger: 向上离开视频区域');
        this.section.classList.remove('video-active');
        this.pauseVideo();
      }
    });
  }



  createContentAnimation() {
    if (!this.contentWrapper) return;

    const title = this.contentWrapper.querySelector('.video-title');
    const description = this.contentWrapper.querySelector('.video-description');
    const videoOverlay = this.section.querySelector('.video-overlay');

    // 创建分阶段的内容动画，包含渐变遮罩
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: this.section,  // 触发元素：视频组件区域
        /* 🎯 滚动触发位置控制：控制滚动到哪里开始显示标题和描述 */
        start: 'top 60%',       // ⚙️ 开始位置：当组件顶部到达视口60%位置时开始动画
        end: 'center center',   // ⚙️ 结束位置：当组件中心到达视口中心时动画完成
        /* 🕐 滚动同步速度控制：优化为更快响应 */
        scrub: 1,               // ⚙️ 滚动绑定：1秒的缓冲，提高响应速度
        invalidateOnRefresh: true,
        // 添加性能优化
        fastScrollEnd: true,
        preventOverlaps: true,
        onStart: () => {
          console.log('内容动画开始 - 包含渐变遮罩');
        },
        onLeave: () => {
          console.log('离开视频区域 - 渐变遮罩淡出');
        },
        onEnterBack: () => {
          console.log('重新进入视频区域 - 渐变遮罩淡入');
        }
      }
    });

    // 第一阶段：渐变遮罩淡入（最早开始，为文字提供背景）
    if (videoOverlay) {
      tl.to(videoOverlay, {
        '--gradient-opacity': 1, // 使用 CSS 自定义属性控制渐变遮罩
        /* 🕐 渐变遮罩动画时间控制：duration=400ms, delay=0ms */
        duration: 0.4,  // ⚙️ 手动调整参数：渐变遮罩淡入速度，增加数值让遮罩出现更慢
        ease: 'power2.out'
      }, 0);  // ⚙️ 立即开始：0ms延迟，最先执行为后续文字提供背景
    }

    // 第二阶段：容器淡入
    tl.to(this.contentWrapper, {
      opacity: 1,
      /* 🕐 容器动画时间控制：duration=300ms, delay=100ms */
      duration: 0.3,  // ⚙️ 手动调整参数：容器淡入速度，影响整体内容显示时机
      ease: 'power2.out'
    }, 0.1)  // ⚙️ 延迟时间：100ms后开始容器淡入，与遮罩形成层次感
    // 第三阶段：标题动画 - 从下方滑入并带有轻微缩放
    .fromTo(title, {
      y: 80,        // 初始位置：向下偏移80px
      opacity: 0,   // 初始透明度：完全透明
      scale: 0.95   // 初始缩放：95%大小
    }, {
      y: 0,         // 最终位置：原始位置
      opacity: 1,   // 最终透明度：完全不透明
      scale: 1,     // 最终缩放：100%大小
      /* 🕐 标题动画持续时间控制：标题从出现到完全显示需要多长时间 */
      duration: 1.5,      // ⚙️ 动画持续时间：0.8秒(800毫秒)
                          // 调整说明：改为0.5让标题出现更快，改为1.2让标题出现更慢
      ease: 'power3.out'  // 缓动函数：快速开始，缓慢结束
    }, 0.3)               // ⚙️ 动画开始延迟：滚动触发后300毫秒才开始标题动画
                          // 调整说明：改为0让标题立即出现，改为0.5让标题延迟更久
    // 第四阶段：描述文字动画 - 与渐变遮罩同步
    .fromTo(description, {
      y: 60,        // 初始位置：向下偏移60px
      opacity: 0    // 初始透明度：完全透明
    }, {
      y: 0,         // 最终位置：原始位置
      opacity: 1,   // 最终透明度：完全不透明
      /* 🕐 描述文本动画持续时间控制：描述从出现到完全显示需要多长时间 */
      duration: 1,        // ⚙️ 动画持续时间：1秒(1000毫秒)
                          // 调整说明：改为0.6让描述出现更快，改为1.5让描述出现更慢
      ease: 'power2.out'  // 缓动函数：快速开始，缓慢结束
    }, 0.7);              // ⚙️ 动画开始延迟：滚动触发后700毫秒才开始描述动画
                          // 调整说明：改为0.3让描述与标题同时出现，改为1.0让描述延迟更久

    this.contentAnimationInstance = tl;
  }

  createGradientMaskAnimation() {
    const videoOverlay = this.section.querySelector('.video-overlay');
    if (!videoOverlay) {
      console.log('❌ 渐变遮罩元素未找到');
      return;
    }

    console.log('✅ 渐变遮罩动画初始化开始');

    // 🎭 正常模式：渐变遮罩由 ScrollTrigger 控制
    console.log('🎭 正常模式：渐变遮罩由 ScrollTrigger 控制');

    // 检查组件初始位置，如果已经在视口中则立即显示遮罩
    const rect = this.section.getBoundingClientRect();
    const isInitiallyVisible = rect.top < window.innerHeight && rect.bottom > 0;

    if (isInitiallyVisible) {
      console.log('🎭 组件初始加载时已在视口中，立即显示渐变遮罩');
      videoOverlay.classList.add('mask-visible');
    }

    // 创建独立的渐变遮罩动画，与视频滚动同步
    this.gradientMaskInstance = ScrollTrigger.create({
      trigger: this.section,
      /* 🎯 触发时机控制：优化滚动触发条件 */
      start: 'top 90%',     // 当组件顶部到达视口90%位置时触发
      end: 'bottom 10%',    // 当组件底部离开视口10%位置时结束
      markers: false,       // 关闭调试标记
      toggleActions: 'play none none reverse', // 进入时播放，离开时反向
      onEnter: () => {
        console.log('🎭 渐变遮罩淡入 - ScrollTrigger onEnter');
        console.log('📍 触发位置: 组件顶部到达视口90%');
        videoOverlay.classList.add('mask-visible');
      },
      onLeave: () => {
        console.log('🎭 渐变遮罩淡出 - ScrollTrigger onLeave');
        console.log('📍 触发位置: 组件底部离开视口10%');
        videoOverlay.classList.remove('mask-visible');
      },
      onEnterBack: () => {
        console.log('🎭 渐变遮罩重新淡入 - ScrollTrigger onEnterBack');
        console.log('📍 触发位置: 向上滚动重新进入');
        videoOverlay.classList.add('mask-visible');
      },
      onLeaveBack: () => {
        console.log('🎭 渐变遮罩向上淡出 - ScrollTrigger onLeaveBack');
        console.log('📍 触发位置: 向上滚动离开');
        videoOverlay.classList.remove('mask-visible');
      },
      onRefresh: () => {
        console.log('🔄 ScrollTrigger 刷新 - 渐变遮罩');
        // 刷新时重新检查位置
        const currentRect = this.section.getBoundingClientRect();
        const isCurrentlyVisible = currentRect.top < window.innerHeight && currentRect.bottom > 0;
        if (isCurrentlyVisible) {
          videoOverlay.classList.add('mask-visible');
        }
      }
    });



    console.log('✅ 渐变遮罩动画初始化完成');
    console.log('📊 初始状态:', {
      isInitiallyVisible,
      rectTop: rect.top,
      rectBottom: rect.bottom,
      windowHeight: window.innerHeight
    });
  }



  // 简化的视频预加载方法
  preloadVideoFrames() {
    if (!this.video || !this.isVideoLoaded) return;

    // 简单的预加载：加载第一帧
    this.video.currentTime = 0.1;
    this.video.currentTime = 0;

    console.log('视频预加载完成');
  }

  // 优化的easing函数
  smoothEasing(t) {
    // 使用更平滑的三次贝塞尔曲线
    return t * t * (3 - 2 * t);
  }

  // 原有的easing函数保留作为备用
  easeInOutQuad(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  setupScrollTriggerRefresh() {
    // 页面加载完成后刷新ScrollTrigger
    window.addEventListener('load', () => {
      ScrollTrigger.refresh();
    });

    // 窗口大小改变时刷新
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        ScrollTrigger.refresh();
      }, 250);
    });
  }

  bindEvents() {
    // 页面可见性变化时暂停/恢复视频
    document.addEventListener('visibilitychange', () => {
      if (!this.video) return;

      if (document.hidden) {
        this.pauseVideo();
      } else if (this.section.classList.contains('video-active')) {
        this.playVideo();
      }
    });

    // 移动端触摸优化
    if ('ontouchstart' in window) {
      this.section.addEventListener('touchstart', () => {
        if (this.video && !this.isVideoLoaded) {
          this.video.load();
        }
      }, { passive: true });
    }

    // 视频播放事件监听
    this.video.addEventListener('play', () => {
      console.log('视频开始播放');
    });

    this.video.addEventListener('pause', () => {
      console.log('视频暂停');
    });

    this.video.addEventListener('ended', () => {
      console.log('视频播放结束');
      // 由于设置了loop，这个事件通常不会触发
    });
  }



  // 公共方法：销毁实例
  destroy() {
    // 停止视频播放
    if (this.video) {
      this.pauseVideo();
    }

    // 清理懒加载观察器
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
      this.intersectionObserver = null;
    }

    // 清理ScrollTrigger实例
    if (this.scrollTriggerInstance) {
      this.scrollTriggerInstance.kill();
    }

    if (this.contentAnimationInstance) {
      this.contentAnimationInstance.kill();
    }

    if (this.gradientMaskInstance) {
      this.gradientMaskInstance.kill();
    }

    // 重置状态
    this.lazyState = 'idle';
    this.isVideoLoaded = false;
    this.isIntersecting = false;

    console.log(`🗑️ IR3 Video Scroll component destroyed for section: ${this.sectionId}`);
  }
}

// 自动初始化所有实例
document.addEventListener('DOMContentLoaded', function() {
  const videoScrollSections = document.querySelectorAll('[id^="ir3-video-scroll-"]');
  
  videoScrollSections.forEach(section => {
    const sectionId = section.id.replace('ir3-video-scroll-', '');
    new IR3VideoScroll(sectionId);
  });
});

// 导出类以供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IR3VideoScroll;
}
