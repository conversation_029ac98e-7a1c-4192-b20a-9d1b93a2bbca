<div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2xKhwQVB8 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gCnZkcUROh",
      "pageContext": {"pageType":"GP_PRODUCT","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":true},
      "setting":{"arrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconColor":"#000000","arrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"arrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"borderActive":{"border":"solid","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":false,"width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false},"dragToScroll":true,"ftArrowIcon":"<svg height=\"20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\"  viewBox=\"0 0 256 256\" fill=\"currentColor\" data-id=\"508817616300409192\">\n              <path fill=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" fill=\"currentColor\" d=\"M180.24,132.24l-80,80a6,6,0,0,1-8.48-8.48L167.51,128,91.76,52.24a6,6,0,0,1,8.48-8.48l80,80A6,6,0,0,1,180.24,132.24Z\" /></svg>","ftArrowIconColor":"#ffffff","ftArrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"ftArrowNavColor":"#E0E0E0","ftArrowNavRadius":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"ftClickOpenLightBox":{"desktop":"none"},"ftDotActiveColor":{"desktop":"line-3","mobile":"#B4B4B4"},"ftDotColor":{"desktop":"bg-1","mobile":"#EEEEEE"},"ftDotGapToCarousel":{"desktop":16,"mobile":"8"},"ftDotSize":{"desktop":12,"mobile":8},"ftDotStyle":{"desktop":"none","mobile":"outside"},"ftDragToScroll":true,"ftLoop":{"desktop":true},"ftNavigationPosition":{"desktop":"inside","mobile":"inside"},"ftPauseOnHover":true,"ftSpeed":1,"galleryHoverEffect":"none","galleryZoom":150,"galleryZoomType":"default","hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"none"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-available-variant","preload":false,"qualityPercent":{"desktop":100},"qualityType":{"desktop":"finest"},"speed":1,"type":{"desktop":"slider"},"typeDisplay":"all-images","zoom":150,"zoomType":"default"},
      "styles":{"align":{"desktop":"center"},"corner":{"bblr":"2px","bbrr":"2px","btlr":"2px","btrr":"2px","radiusType":"custom"},"ftCorner":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"ftLayout":{"desktop":"cover"},"ftShape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"itemSpacing":{"desktop":"12px","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover"},"position":{"desktop":"bottom-center","mobile":"only-feature"},"ratioLayout":{"desktop":[2,10]},"ratioLayoutRight":{"desktop":[10,2]},"shape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor1Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor2Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"}},"shapeForBottom":{"desktop":{"gap":"","height":"","shape":"original","shapeLinked":true,"width":"20%"}},"shapeForFtOnly":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeForInside":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForInsideBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"spacing":{"desktop":"16px","mobile":"var(--g-s-s)"}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      data-id="gCnZkcUROh"
      class="gCnZkcUROh gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:16px;--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:16px;--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:center"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}} gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      
    <gp-carousel data-id="gp-carousel-gCnZkcUROh" type="gp-feature-image-carousel" product-id="{{product.id}}" product-media="{{product.media.size}}" id="gp-root-carousel-ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}-{{section.id}}" class="
          gp-px-0 tablet:gp-px-0 mobile:gp-px-0
          gp-flex-1 gp-w-full gp-feature-image-carousel
          gp-group/carousel gp-flex" gp-data='{"id":"ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}-{{section.id}}","setting":{"loop":{"desktop":true},"slidesToShow":{"desktop":1},"dotStyle":{"desktop":"none","tablet":"none","mobile":"outside"},"dotSize":{"desktop":12,"mobile":8},"dotGapToCarousel":{"desktop":16,"mobile":"8"},"dotColor":{"desktop":"bg-1","mobile":"#EEEEEE"},"dotActiveColor":{"desktop":"line-3","mobile":"#B4B4B4"},"controlOverContent":{"desktop":true,"tablet":true,"mobile":true},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"arrowCustom":"<svg height=\"20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\"  viewBox=\"0 0 256 256\" fill=\"currentColor\" data-id=\"508817616300409192\">\n              <path fill=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" fill=\"currentColor\" d=\"M180.24,132.24l-80,80a6,6,0,0,1-8.48-8.48L167.51,128,91.76,52.24a6,6,0,0,1,8.48-8.48l80,80A6,6,0,0,1,180.24,132.24Z\" /></svg>","arrowCustomColor":"#ffffff","arrowBackgroundColor":"#E0E0E0","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"inside","mobile":"inside"},"arrowButtonSize":{"desktop":{"width":"24px","height":"24px"}}},"styles":{"sizeSetting":{"desktop":{"width":"100%","height":"auto"},"tablet":{"width":"100%","height":"auto"},"mobile":{"width":"100%","height":"auto"}},"align":{"desktop":"center","tablet":"center","mobile":"center"}},"isHiddenArrowWhenDisabled":true}' style="--jc:center;--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gp-carousel-gCnZkcUROh gp-featured-image-wrapper"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:auto;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}} gp-carousel-action-back gem-slider-previous ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gCnZkcUROh gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:16px;--top:initial;--right:initial;--bottom:;--left-tablet:16px;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:16px;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:#E0E0E0;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#ffffff;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817616300409192">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M180.24,132.24l-80,80a6,6,0,0,1-8.48-8.48L167.51,128,91.76,52.24a6,6,0,0,1,8.48-8.48l80,80A6,6,0,0,1,180.24,132.24Z" /></svg>
    </div>
      <style>
    .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh {
      
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
    }
    .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh {
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh {
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:undefinedpx;--cg-tablet:undefinedpx;--cg:undefinedpx">
          
          {%- if product.media.size > 0 -%}
            
      
    
            {% assign largestRatio = 0 %}
            {% for featureMedia in product.media %}
              {% assign height = featureMedia.height | times: 1.0 %}
              {% assign width = featureMedia.width | times: 1.0 %}
              {% assign ratio = height | divided_by: width %}
              {% if ratio > largestRatio %}
                {% assign largestRatio = ratio %}
              {% endif %}
            {% endfor %}
            {%- for featureMedia in product.media -%}
              {%- if featureMedia.media_type == 'image' -%}
                {%- for image in product.images -%}
                  {% if image.src == featureMedia.src %}
                    {% assign imageID = image.id %}
                    {% break %}
                  {% endif%}
                {% endfor %}
              {%- else -%}
                {% assign imageID = '' %}
              {%- endif -%}
              
    {% assign productImageWidth = 0 %}
    {% case featureMedia.media_type %}
      {% when 'image' %}
        {% assign productImageWidth = featureMedia.width %}
      {% else %}
        {% assign productImageWidth = featureMedia.preview_image.width %}
    {% endcase %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      id="{{section.id}}-{{imageID}}"
      style="width:{{productImageWidth}}px;--minw:calc(100% / 4 - 33px);--minw-tablet:calc(100% / 4 - 33px);--minw-mobile:calc(100% / 4 - 33px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);outline-color:var(--g-c-brand, brand);--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--h:auto;--h-tablet:auto;--h-mobile:auto;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}} gp-child-item-undefined gp-group gp-z-0 gp-flex !gp-min-w-full !gp-max-w-full gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-outline-1 -gp-outline-offset-1 gp-image-item gp-ft-image-item data-[outline=active]:gp-outline undefined"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div 
        class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image'  %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
      >
        
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}} gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
    {% endcase %}
    
      </div>
      
      </div>
    </div>
  
            {% endfor %}
          {%- else -%}
            
  <img
      id="noImageError"
      
      draggable="false"
      class="gp-w-full featured-image-only !gp-rounded-none"
      data-src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" data-srcset="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgwIiBoZWlnaHQ9IjQ4MCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNDgwLTQ4MCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNDgwIiBoZWlnaHQ9IjQ4MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDgwIiBoZWlnaHQ9IjQ4MCIgZmlsbD0idXJsKCNnLTQ4MC00ODApIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii00ODAiIHRvPSI0ODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="480" height="480" alt="no image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover;height:100%"
    />
  
          {%- endif -%}
        
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}} gp-carousel-action-next gem-slider-next ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gCnZkcUROh gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:initial;--top:;--right:16px;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:16px;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:16px;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:#E0E0E0;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#ffffff;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817616300409192">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M180.24,132.24l-80,80a6,6,0,0,1-8.48-8.48L167.51,128,91.76,52.24a6,6,0,0,1,8.48-8.48l80,80A6,6,0,0,1,180.24,132.24Z" /></svg>
    </div>
      <style>
    .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh {
      
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
    }
    .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh {
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh {
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
      .ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 999999px;
      border-bottom-right-radius: 999999px;
      border-top-left-radius: 999999px;
      border-top-right-radius: 999999px;
      
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-ft-gp-carousel-gCnZkcUROh-{{section.id}}-{{product.id}}-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--mt-mobile:8px;--d:none;--d-tablet:none;--d-mobile:flex"
 ></div>
      </div>
    </gp-carousel>
    {% if product.media.size > 1 %} <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script> {% endif %}

  
    
      
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
        {%- if product.media.size > 1 -%}
            <div
              class="gallery-wrapper gp-product-images-gallery gp-flex gp-overflow-hidden gp-max-w-full gp-max-h-full data-[only-image=true]:gp-hidden"
              style="--o:1;--o-tablet:1;--o-mobile:1;--jc:center;--pos:static;--pos-tablet:static;--pos-mobile:static;--w:100%;--w-tablet:100%;--w-mobile:100%;--bottom:auto;--bottom-tablet:auto;--bottom-mobile:auto;--top:auto;--top-tablet:auto;--top-mobile:auto;--left:auto;--left-tablet:auto;--left-mobile:auto;--right:auto;--right-tablet:auto;--right-mobile:auto"
              data-only-image="{%- if product.media.size > 1 -%}false{%- else -%}true{%- endif -%}"
            >
              <style>
    .gem-slider-item-gCnZkcUROh-{{product.id}}.gp-gallery-image-item::after, .gem-slider-item-gp-gallery-gCnZkcUROh-{{product.id}}.gp-gallery-image-item::after {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      z-index: 1000;
      top: 0;
      left: 0;
      border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #000000;
  
      
      border-bottom-left-radius: 2px;
      border-bottom-right-radius: 2px;
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
      
    }
    .gem-slider-item-gCnZkcUROh-{{product.id}}.gp-gallery-image-item[data-outline=active]:after, .gem-slider-item-gp-gallery-gCnZkcUROh-{{product.id}}.gp-gallery-image-item[data-outline=active]:after {
      pointer-events: none;
    }
  </style>
              
    
    <gp-carousel data-id="gp-gallery-gCnZkcUROh" type="grid-carousel" product-media="{{product.media.size}}" id="gp-root-carousel-gp-gallery-gCnZkcUROh-{{product.id}}-{{section.id}}" class="gp-flex-1 gp-w-full carousel-gallery gp-px-0 tablet:gp-px-0 mobile:gp-px-0  gp-group/carousel gp-flex" gp-data='{"id":"gp-gallery-gCnZkcUROh-{{product.id}}-{{section.id}}","setting":{"loop":{"desktop":true},"itemNumber":{"desktop":5,"tablet":5,"mobile":"auto"},"dot":{"desktop":false,"tablet":false,"mobile":false},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"controlOverContent":{"desktop":false,"tablet":false,"mobile":false},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"vertical":{"desktop":false,"mobile":false,"tablet":false},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"none"},"arrowButtonSize":{"desktop":{"width":"24px","height":"24px"}}},"styles":{"sizeSetting":{"desktop":{"width":"100%","height":"auto"},"tablet":{"width":"100%","height":"auto"},"mobile":{"width":"100%","height":"auto"}},"spacing":{"desktop":12,"tablet":5,"mobile":5}},"isHiddenArrowWhenDisabled":true}' style="--d:flex;--d-mobile:none;--d-tablet:flex">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gp-gallery-gCnZkcUROh  gp-py-0 tablet:gp-py-0 mobile:gp-py-0"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-gallery-gCnZkcUROh-{{product.id}} gp-carousel-action-back gem-slider-previous gp-gallery-gCnZkcUROh-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-gallery-gCnZkcUROh gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gp-gallery-gCnZkcUROh-{{product.id}}-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:5px;--cg-tablet:5px;--cg:12px">
          
    {%- if product.media.size > 1 -%}
      {%- for media in product.media -%}
      {% if media.media_type == 'image' %}
        {%- for image in product.images -%}
          {% if image.src == media.src %}
            {% assign imageID = image.id %}
            {% break %}
          {% endif%}
        {% endfor %}
      {% else %}
        {% assign imageID = '' %}
      {% endif %}
        {%- if media.id == product.featured_media.id -%}
          
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
    {% endif %}
      {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="active"
      id="{{imageID}}"
      style="--minw:calc(20% - 9.6px);--minw-tablet:calc(20% - 9.6px);--minw-mobile:100%;--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);maxWidth:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:2px;--bbrr:2px;--btlr:2px;--btrr:2px;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-gallery-gCnZkcUROh-{{product.id}} gp-child-item-undefined gp-group gp-flex gp-w-full gp-relative gp-items-center gp-justify-center gp-overflow-hidden gp-image-item gp-gallery-image-item gp-cursor-pointer active data-[outline=deactive]:after:!gp-border-transparent"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div class="gp-w-full gp-relative"
        style="--pb: calc(({%if media.height%} {{media.height}} / {{media.width}} {%else%} 100 / 100 {%endif%})*100%);--pb-tablet: calc(({%if media.height%} {{media.height}} / {{media.width}} {%else%} 100 / 100 {%endif%})*100%);--pb-mobile: calc((1/1)*100%);;"
      >
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 gp-cursor-pointer"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLXt7bWVkaWEud2lkdGh9fS17e21lZGlhLmhlaWdodH19Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSJ7e21lZGlhLndpZHRofX0iIGhlaWdodD0ie3ttZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIGZpbGw9InVybCgjZy17e21lZGlhLndpZHRofX0te3ttZWRpYS5oZWlnaHR9fSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLXt7bWVkaWEud2lkdGh9fSIgdG89Int7bWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{media.width}}/{{media.height}};--aspect-tablet:{{media.width}}/{{media.height}};--aspect-mobile:1/1;--objf:cover;width:100%;height:100%;cursor:pointer"
    />
  

    {% if media.media_type == 'video' or media.media_type == 'external_video' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
      </svg>
    </div>
    {% endif %}

    {% if media.media_type == 'model' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
      </svg>
    </div>
    {% endif %}

    <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
    style="--d: none;--d-mobile: none;--d-tablet: none;"
    >
      <svg
        height="100%"
        width="100%"
        xmlns="http://www.w3.org/2000/svg"
        class="gp-h-6 gp-w-6"
        viewBox="0 0 512 512"
        color="#fff"
      >
        <path
          fill="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
        />
      </svg>
    </div>
    </div>
    
      </div>
    </div>
  
        {% else %}
          
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
    {% endif %}
      {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="deactive"
      id="{{imageID}}"
      style="--minw:calc(20% - 9.6px);--minw-tablet:calc(20% - 9.6px);--minw-mobile:100%;--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);maxWidth:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:2px;--bbrr:2px;--btlr:2px;--btrr:2px;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-gallery-gCnZkcUROh-{{product.id}} gp-child-item-undefined gp-group gp-flex gp-w-full gp-relative gp-items-center gp-justify-center gp-overflow-hidden gp-image-item gp-gallery-image-item gp-cursor-pointer undefined data-[outline=deactive]:after:!gp-border-transparent"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div class="gp-w-full gp-relative"
        style="--pb: calc(({%if media.height%} {{media.height}} / {{media.width}} {%else%} 100 / 100 {%endif%})*100%);--pb-tablet: calc(({%if media.height%} {{media.height}} / {{media.width}} {%else%} 100 / 100 {%endif%})*100%);--pb-mobile: calc((1/1)*100%);;"
      >
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 gp-cursor-pointer"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLXt7bWVkaWEud2lkdGh9fS17e21lZGlhLmhlaWdodH19Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSJ7e21lZGlhLndpZHRofX0iIGhlaWdodD0ie3ttZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIGZpbGw9InVybCgjZy17e21lZGlhLndpZHRofX0te3ttZWRpYS5oZWlnaHR9fSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLXt7bWVkaWEud2lkdGh9fSIgdG89Int7bWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{media.width}}/{{media.height}};--aspect-tablet:{{media.width}}/{{media.height}};--aspect-mobile:1/1;--objf:cover;width:100%;height:100%;cursor:pointer"
    />
  

    {% if media.media_type == 'video' or media.media_type == 'external_video' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
      </svg>
    </div>
    {% endif %}

    {% if media.media_type == 'model' %}
    <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
      </svg>
    </div>
    {% endif %}

    <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
    style="--d: none;--d-mobile: none;--d-tablet: none;"
    >
      <svg
        height="100%"
        width="100%"
        xmlns="http://www.w3.org/2000/svg"
        class="gp-h-6 gp-w-6"
        viewBox="0 0 512 512"
        color="#fff"
      >
        <path
          fill="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
        />
      </svg>
    </div>
    </div>
    
      </div>
    </div>
  
        {%- endif -%}
      {% endfor %}
    {%- endif -%}
    
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-gallery-gCnZkcUROh-{{product.id}} gp-carousel-action-next gem-slider-next gp-gallery-gCnZkcUROh-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-gallery-gCnZkcUROh gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-gCnZkcUROh-{{product.id}}.gp-carousel-arrow-gp-gallery-gCnZkcUROh::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gp-gallery-gCnZkcUROh-{{product.id}}-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:0px;--bottom-tablet:0px;--bottom-mobile:0px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
  
            </div>
            
  <div type="grid-gallery"
    class="grid-gallery gp-flex !gp-h-fit gp-w-full gp-flex-wrap scrollbar-track:gp-rounded-2xl scrollbar-thumb:gp-rounded-2xl scrollbar-thumb:gp-bg-gray-400"
    style="--d:none;--d-mobile:none;--d-tablet:none;--cg:12px;--cg-mobile:var(--g-s-s);--rg:12px;--rg-mobile:var(--g-s-s);--jc:center;--pos:static;--pos-tablet:static;--pos-mobile:static;--w:100%;--w-tablet:100%;--w-mobile:100%;--bottom:auto;--bottom-tablet:auto;--bottom-mobile:auto;--top:auto;--top-tablet:auto;--top-mobile:auto;--left:auto;--left-tablet:auto;--left-mobile:auto;--right:auto;--right-tablet:auto;--right-mobile:auto"
  >
    {%- for media in product.media -%}
      {% if media.media_type == 'image' %}
        {%- for image in product.images -%}
          {% if image.src == media.src %}
            {% assign imageID = image.id %}
            {% break %}
          {% endif%}
        {% endfor %}
      {% else %}
        {% assign imageID = '' %}
      {% endif %}
      {%- if media.id == product.featured_media.id -%}
        
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
      {% assign mediaSource = media | json %}
    {% endif %}
    {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    <div
      aria-hidden
      id="{{imageID}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="active"
      class="active gem-slider-item-gCnZkcUROh-{{product.id}} gp-group gp-relative gp-overflow-hidden gp-cursor-pointer gp-image-item gp-gallery-image-item data-[outline=deactive]:after:!gp-border-transparent"
      style="max-width: 100%;--w: calc(20% - 12px * 4 / 5);--w-tablet: calc(20% - 12px * 4 / 5);--w-mobile: calc(100% - var(--g-s-s) * 0 / 1);--h: auto;--h-tablet: auto;--h-mobile: auto;--bblr: 2px;--bbrr: 2px;--btlr: 2px;--btrr: 2px;"
      >
      <div class="gp-h-full" style="{% if media == null or media.media_type == 'image' %} display: block !important; {% endif %} --d: block;--d-tablet: block;--d-mobile: block;">
        {% if media != null %}
          
      {% if media.media_type == 'video' or media.media_type == 'external_video' %}
        <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
            <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
          </svg>
        </div>
      {% endif %}

      {% if media.media_type == 'model' %}
      <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
        </svg>
      </div>
      {% endif %}

      
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp_lazyload"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLXt7bWVkaWEud2lkdGh9fS17e21lZGlhLmhlaWdodH19Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSJ7e21lZGlhLndpZHRofX0iIGhlaWdodD0ie3ttZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIGZpbGw9InVybCgjZy17e21lZGlhLndpZHRofX0te3ttZWRpYS5oZWlnaHR9fSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLXt7bWVkaWEud2lkdGh9fSIgdG89Int7bWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{media.width}}/{{media.height}};--aspect-tablet:{{media.width}}/{{media.height}};--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    

      <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
        style="--d: none;--d-mobile: none;--d-tablet: none;" >
        <svg
          height="100%"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          class="gp-h-6 gp-w-6"
          viewBox="0 0 512 512"
          color="#fff"
        >
          <path
            fill="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
          />
        </svg>
      </div>
    
        {% else media == null %}
          
    
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:1/1;--objf:cover"
    />
  
        {% endif %}
      </div>

      {% assign isMedia= false %}
      {% if media.media_type == 'external_video' or media.media_type == 'video' or media.media_type == 'model' %}
        {% assign isMedia = true %}
      {% endif %}
      <div class="{% if isMedia %}
      gp-flex gp-justify-center gp-items-center
      {% endif %}"
      style="
        {% if isMedia %}
          --aspect-mobile: 1/1;
        {% endif %}
        --d: none;--d-tablet: none;--d-mobile: none;"
      >
        {% case media.media_type %}
          {% when 'external_video' %}
            {% assign mediaSourceVideo = media | external_video_url %}
            
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{media.alt}}"
      
    >
    </iframe>
  
          {% when 'video' %}
            {% assign mediaSourceVideo = media.sources.last.url %}
            
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{media.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
          {% when 'model' %}
            
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%" height="100%"
      
      
      
      
      src="{% if media.sources.first.url contains '.glb' %} {{ media.sources.first.url }}{% else %}{{media.sources.last.url}}{% endif %}"
      alt="{{media.preview_image.alt}}"
      camera-controls="true"
      poster="{{media.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
        {% endcase %}
      </div>
    </div>
      {% else %}
        
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
      {% assign mediaSource = media | json %}
    {% endif %}
    {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    <div
      aria-hidden
      id="{{imageID}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="deactive"
      class="gem-slider-item-gCnZkcUROh-{{product.id}} gp-group gp-relative gp-overflow-hidden gp-cursor-pointer gp-image-item gp-gallery-image-item data-[outline=deactive]:after:!gp-border-transparent"
      style="max-width: 100%;--w: calc(20% - 12px * 4 / 5);--w-tablet: calc(20% - 12px * 4 / 5);--w-mobile: calc(100% - var(--g-s-s) * 0 / 1);--h: auto;--h-tablet: auto;--h-mobile: auto;--bblr: 2px;--bbrr: 2px;--btlr: 2px;--btrr: 2px;"
      >
      <div class="gp-h-full" style="{% if media == null or media.media_type == 'image' %} display: block !important; {% endif %} --d: block;--d-tablet: block;--d-mobile: block;">
        {% if media != null %}
          
      {% if media.media_type == 'video' or media.media_type == 'external_video' %}
        <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
            <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
          </svg>
        </div>
      {% endif %}

      {% if media.media_type == 'model' %}
      <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
        </svg>
      </div>
      {% endif %}

      
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp_lazyload"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLXt7bWVkaWEud2lkdGh9fS17e21lZGlhLmhlaWdodH19Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSJ7e21lZGlhLndpZHRofX0iIGhlaWdodD0ie3ttZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIGZpbGw9InVybCgjZy17e21lZGlhLndpZHRofX0te3ttZWRpYS5oZWlnaHR9fSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLXt7bWVkaWEud2lkdGh9fSIgdG89Int7bWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{media.width}}/{{media.height}};--aspect-tablet:{{media.width}}/{{media.height}};--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    

      <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
        style="--d: none;--d-mobile: none;--d-tablet: none;" >
        <svg
          height="100%"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          class="gp-h-6 gp-w-6"
          viewBox="0 0 512 512"
          color="#fff"
        >
          <path
            fill="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
          />
        </svg>
      </div>
    
        {% else media == null %}
          
    
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:1/1;--objf:cover"
    />
  
        {% endif %}
      </div>

      {% assign isMedia= false %}
      {% if media.media_type == 'external_video' or media.media_type == 'video' or media.media_type == 'model' %}
        {% assign isMedia = true %}
      {% endif %}
      <div class="{% if isMedia %}
      gp-flex gp-justify-center gp-items-center
      {% endif %}"
      style="
        {% if isMedia %}
          --aspect-mobile: 1/1;
        {% endif %}
        --d: none;--d-tablet: none;--d-mobile: none;"
      >
        {% case media.media_type %}
          {% when 'external_video' %}
            {% assign mediaSourceVideo = media | external_video_url %}
            
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{media.alt}}"
      
    >
    </iframe>
  
          {% when 'video' %}
            {% assign mediaSourceVideo = media.sources.last.url %}
            
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{media.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
          {% when 'model' %}
            
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%" height="100%"
      
      
      
      
      src="{% if media.sources.first.url contains '.glb' %} {{ media.sources.first.url }}{% else %}{{media.sources.last.url}}{% endif %}"
      alt="{{media.preview_image.alt}}"
      camera-controls="true"
      poster="{{media.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
        {% endcase %}
      </div>
    </div>
      {%- endif -%}
    {% endfor %}
    {%- if product.media.size > 0 -%}
      
      
    
    {%- endif -%}
    {%- if product.media.size < 1 -%}
      
    {% if media.media_type == 'video' %}
      {% assign mediaSourceUrl = media.sources.last.url %}
    {% endif %}
    {% if media.media_type == 'external_video' %}
      {% assign mediaSourceUrl = media | external_video_url %}
      {% assign mediaSource = media | json %}
    {% endif %}
    {% if media.media_type == 'image' %}
      {% assign mediaSourceUrl = media.src %}
    {% endif %}
    <div
      aria-hidden
      id="{{imageID}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      data-outline="deactive"
      class="gem-slider-item-gCnZkcUROh-{{product.id}} gp-group gp-relative gp-overflow-hidden gp-cursor-pointer gp-image-item gp-gallery-image-item data-[outline=deactive]:after:!gp-border-transparent"
      style="max-width: 100%;--w: calc(20% - 12px * 4 / 5);--w-tablet: calc(20% - 12px * 4 / 5);--w-mobile: calc(100% - var(--g-s-s) * 0 / 1);--h: auto;--h-tablet: auto;--h-mobile: auto;--bblr: 2px;--bbrr: 2px;--btlr: 2px;--btrr: 2px;"
      >
      <div class="gp-h-full" style="{% if media == null or media.media_type == 'image' %} display: block !important; {% endif %} --d: block;--d-tablet: block;--d-mobile: block;">
        {% if media != null %}
          
      {% if media.media_type == 'video' or media.media_type == 'external_video' %}
        <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
            <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
          </svg>
        </div>
      {% endif %}

      {% if media.media_type == 'model' %}
      <div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
        </svg>
      </div>
      {% endif %}

      
      
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp_lazyload"
      data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | img_url: '480x480'}} 480w, {{media.preview_image | img_url: '768x768'}} 768w,{{media.preview_image | img_url: '1024x1024'}} 1024w,{{media.preview_image | img_url: '1440x1440'}} 1440w" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLXt7bWVkaWEud2lkdGh9fS17e21lZGlhLmhlaWdodH19Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSJ7e21lZGlhLndpZHRofX0iIGhlaWdodD0ie3ttZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIGZpbGw9InVybCgjZy17e21lZGlhLndpZHRofX0te3ttZWRpYS5oZWlnaHR9fSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLXt7bWVkaWEud2lkdGh9fSIgdG89Int7bWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{media.width}}" height="{{media.height}}" alt="{{media.alt}}" base-src="{{media.preview_image | image_url}}"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{media.width}}/{{media.height}};--aspect-tablet:{{media.width}}/{{media.height}};--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    

      <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
        style="--d: none;--d-mobile: none;--d-tablet: none;" >
        <svg
          height="100%"
          width="100%"
          xmlns="http://www.w3.org/2000/svg"
          class="gp-h-6 gp-w-6"
          viewBox="0 0 512 512"
          color="#fff"
        >
          <path
            fill="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
          />
        </svg>
      </div>
    
        {% else media == null %}
          
    
  <img
      
      
      draggable="false"
      class="!gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:1/1;--objf:cover"
    />
  
        {% endif %}
      </div>

      {% assign isMedia= false %}
      {% if media.media_type == 'external_video' or media.media_type == 'video' or media.media_type == 'model' %}
        {% assign isMedia = true %}
      {% endif %}
      <div class="{% if isMedia %}
      gp-flex gp-justify-center gp-items-center
      {% endif %}"
      style="
        {% if isMedia %}
          --aspect-mobile: 1/1;
        {% endif %}
        --d: none;--d-tablet: none;--d-mobile: none;"
      >
        {% case media.media_type %}
          {% when 'external_video' %}
            {% assign mediaSourceVideo = media | external_video_url %}
            
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{media.alt}}"
      
    >
    </iframe>
  
          {% when 'video' %}
            {% assign mediaSourceVideo = media.sources.last.url %}
            
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{media.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
          {% when 'model' %}
            
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%" height="100%"
      
      
      
      
      src="{% if media.sources.first.url contains '.glb' %} {{ media.sources.first.url }}{% else %}{{media.sources.last.url}}{% endif %}"
      alt="{{media.preview_image.alt}}"
      camera-controls="true"
      poster="{{media.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
        {% endcase %}
      </div>
    </div>
    {%- endif -%}
  </div>
          {% endif %}
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="gXGwA-vgFp ">
      
    {%- liquid
      assign current_variant = variant
      assign available = current_variant.available | default: false
      assign inventory_management = current_variant.inventory_management | default: null
      assign inventory_policy = current_variant.inventory_policy | default: 'deny'
      assign inventory_quantity = current_variant.inventory_quantity | default: 0
      assign scarcityThreshold = true
      assign check_display = true

      if check_display == true
        assign should_display = true
      elsif true and inventory_quantity >= 0 and inventory_quantity < 12
        assign should_display = true
      else
        assign should_display = false
      endif

      if false and available == false
        assign should_display = false
      endif

      if true and available
        assign show_progress_bar = true
      else
        assign show_progress_bar = false
      endif
    -%}
    {% capture variant_policy_list %}
      {
        {% for variant in product.variants %}
        "{{ variant.id }}":"{{ variant.inventory_policy }}",
        "{{ variant.id }}_management":"{{ variant.inventory_management }}"{% if forloop.last == false %},{% endif %}
        {% endfor %}
      }
    {% endcapture %}
    <gp-product-stock-counter
    data-id="gXGwA-vgFp"
    data-visible="{{ should_display }}"
    data-policy='{{ variant_policy_list }}'
    gp-setting='{"displayByDefault":true,"displayProgressBar":true,"scarcityThreshold":12,"template":"<mark>HURRY!</mark> ONLY %number% LEFT","outOfStockMessage":"OUT OF STOCK","continueSelling":"<mark>Restock soon!</mark> PREORDER NOW","unlimitedQuantityMessage":"<mark>HURRY!</mark> LET BUY NOW","color":"text-1","hiddenCounterWhenOutOfStock":false}'
    class="gp-product-stock-counter data-[visible=true]:gp-block data-[visible=false]:gp-hidden "
      style="--ta:left">
        <div
          class="gp-inline-block"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--c:var(--g-c-text-1, text-1)"
        >
          <div class="gp-flex gp-items-baseline">
            
            <div
            class="gp-mr-1 gp-shrink-0 gp-bg-[length:60px_100%] gp-stock-counter-icon"
            style="--c:var(--g-c-text-2, text-2);--w:var(--g-p1-size)"
          >
          <svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor"> <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M176.64 11.488A16 16 0 0 1 192 0h128a16 16 0 0 1 15.168 21.056L278.208 192H400a16 16 0 0 1 12.64 25.824l-224 288a16 16 0 0 1 -27.936 -14.528L218.336 304H112a16 16 0 0 1 -15.36 -20.512l80 -272z" /></svg></div>
            
            <div class="gp-hidden gp-text-white gp-py-1 gp-px-2 gp-mr-1 gp-inline-block gp-my-[1px] gp-text-g-text-1 gp-bg-g-text-1" >
              <div id="template">{{ section.settings.ggXGwA-vgFp_template | replace: '<$quantity$>', inventory_quantity | replace: '&lt;$quantity$&gt;', inventory_quantity }}</div>
              <div id="out-of-stock-message">{{ section.settings.ggXGwA-vgFp_outOfStockMessage }}</div>
              <div id="continue-selling">{{ section.settings.ggXGwA-vgFp_continueSelling }}</div>
              <div id="unlimited-quantity-message">{{ section.settings.ggXGwA-vgFp_unlimitedQuantityMessage }}</div>
            </div>
            <p
              data-slot-type="message"
              class="gp-g-paragraph-1 gp-stock-counter-mark"
              style="--c:var(--g-c-text-1, text-1)"
            >
              {% if inventory_management == null and inventory_quantity <= 0 %}
                {{ section.settings.ggXGwA-vgFp_unlimitedQuantityMessage }}
              {% elsif available == false %}
                {{ section.settings.ggXGwA-vgFp_outOfStockMessage }}
              {% elsif available and inventory_policy == 'continue' and inventory_quantity <= 0 %}
                {{ section.settings.ggXGwA-vgFp_continueSelling }}
              {% else %}
                {{ section.settings.ggXGwA-vgFp_template | replace: '<$quantity$>', inventory_quantity | replace: '&lt;$quantity$&gt;', inventory_quantity }}
              {% endif %}
            </p>
          </div>

          <div
          data-slot-type="progress-bar"
          data-visible="{{ show_progress_bar }}"
            style="--h:10px;--bg:linear-gradient(90deg, #eee 50%, transparent 50%);--bgc:var(--g-c-line-3, line-3);--bgp:20% center"
            class="gp-stock-counter-progress gp-relative gp-mt-2 gp-w-full -gp-scale-100 data-[visible=true]:gp-block data-[visible=false]:gp-hidden !gp-bg-[length:200%] gp-rounded-md  before:gp-absolute before:gp-left-0 before:gp-h-full before:gp-w-full before:gp-animate-shift before:gp-content-[''] before:gp-bg-stock-counter-animate before:gp-bg-[length:60px_100%] before:gp-shadow-stock-counter before:gp-rounded-md"
          ></div>
        </div>
      </gp-product-stock-counter>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-stock-counter.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
       
      
    <div
      parentTag="Col" id="gb87paWDWw" data-id="gb87paWDWw"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gb87paWDWw gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gJikIJmd-j gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g1mV-6W45d" data-id="g1mV-6W45d"
        style="--pt:var(--g-s-s);--pl:var(--g-s-s);--pb:var(--g-s-s);--pr:var(--g-s-s);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:3px;--bbrr:3px;--btlr:3px;--btrr:3px;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 9fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 8fr) minmax(0, 4fr);--w:100%;--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:#E1EFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g1mV-6W45d gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:center"
      class="gkTn06vIvB gp-relative gp-flex gp-flex-col"
    >
      <div
      
      
      class="g0vWaJoSIq gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="gmOwR35HA5 ">
      
    <gp-countdown-timer
      data-id="gmOwR35HA5"
     class="gp-flex "
     gp-data='{"behaviour":"standard","dateStandard":1751437370168,"timeZone":"UTC-4","timerDaily":{"hours":10,"mins":30,"format":"am"},"timerEverygreen":{"days":0,"hours":3,"mins":0,"endTime":1750843370168,"startTime":1750832570168},"loopAfterFinish":true,"enableDay":true,"dayLabel":"Days","enableHour":true,"hourLabel":"Hours","enableMinute":true,"minuteLabel":"Minutes","enableSecond":true,"secondLabel":"Seconds","enableWeek":false,"weekLabel":"Weeks","translate":"dayLabel,hourLabel,minuteLabel,secondLabel,weekLabel","redirectUrl":{"link":"#","target":"_self"},"uid":"gmOwR35HA5","builderData":{"uid":"gmOwR35HA5","tag":"Countdown","label":"Countdown Timer","settings":{"behaviour":"standard","dateStandard":1751437370168,"timeZone":"UTC-4","timerDaily":{"hours":10,"mins":30,"format":"am"},"timerEverygreen":{"days":0,"hours":3,"mins":0,"endTime":1750843370168,"startTime":1750832570168},"loopAfterFinish":true,"enableDay":true,"dayLabel":"Days","enableHour":true,"hourLabel":"Hours","enableMinute":true,"minuteLabel":"Minutes","enableSecond":true,"secondLabel":"Seconds","enableWeek":false,"weekLabel":"Weeks","translate":"dayLabel,hourLabel,minuteLabel,secondLabel,weekLabel","redirectUrl":{"link":"#","target":"_self"}},"styles":{"numTypo":{"type":"subheading-2","attrs":{"color":"text-2","bold":true}},"colorNumber":"text-2","labelTypo":{"type":"paragraph-2","attrs":{"color":"text-1"}},"colorLabel":"text-1","textAlign":{"desktop":"center"},"backgroundItemColor":"bg-2","verticalGap":"0px","horizontalGap":"8px","itemPadding":{"type":"medium"},"borderState":{"normal":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","isCustom":false}},"roundedState":{"normal":{"radiusType":"none"}}},"advanced":{"spacing-setting":{"desktop":{"margin":{"bottom":0}}},"d":{"desktop":true,"tablet":true,"mobile":true},"border":{"desktop":{"normal":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","color":"#121212","isCustom":true}}},"rounded":{"desktop":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"none"}}},"hasBoxShadow":{"desktop":{"normal":false}},"boxShadow":{"desktop":{"normal":{"type":"shadow-1","distance":"4px","blur":"4px","spread":"4px","color":"rgba(18, 18, 18, 0.7)","angle":49}}},"op":{"desktop":"100%"}},"childrens":[],"type":"component"}}'
     gp-href="#"
     style="--jc:center"
     >
      <div id="section-countdown" class="gp-flex gp-flex-wrap gp-w-max gp-relative" style="gap:8px;--jc:center">
        <a
            href="#"
            target="_self"
            aria-label="Countdown link"
            class="gp-absolute gp-inset-0 gp-z-1"
          > </a>
          
          
    <div
        style="--bs:none;--bw:1px 1px 1px 1px;--bg:var(--g-c-bg-2, bg-2)"
    class="gp-bg-g-bg-2 gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-medium">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      id="day"
        class="gmOwR35HA5 "
        style="--mb:0px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number gp-g-subheading-2"
          style="--w:100%;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      
        class="gmOwR35HA5 "
        style="--c:var(--g-c-text-1, text-1)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-2"
          style="--w:100%;--c:var(--g-c-text-1, text-1);word-break:break-word;overflow:hidden"
        >{{ section.settings.ggmOwR35HA5_dayLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:1px 1px 1px 1px;--bg:var(--g-c-bg-2, bg-2)"
    class="gp-bg-g-bg-2 gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-medium">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      id="hour"
        class="gmOwR35HA5 "
        style="--mb:0px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number gp-g-subheading-2"
          style="--w:100%;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      
        class="gmOwR35HA5 "
        style="--c:var(--g-c-text-1, text-1)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-2"
          style="--w:100%;--c:var(--g-c-text-1, text-1);word-break:break-word;overflow:hidden"
        >{{ section.settings.ggmOwR35HA5_hourLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:1px 1px 1px 1px;--bg:var(--g-c-bg-2, bg-2)"
    class="gp-bg-g-bg-2 gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-medium">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      id="minute"
        class="gmOwR35HA5 "
        style="--mb:0px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number gp-g-subheading-2"
          style="--w:100%;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      
        class="gmOwR35HA5 "
        style="--c:var(--g-c-text-1, text-1)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-2"
          style="--w:100%;--c:var(--g-c-text-1, text-1);word-break:break-word;overflow:hidden"
        >{{ section.settings.ggmOwR35HA5_minuteLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
          
    <div
        style="--bs:none;--bw:1px 1px 1px 1px;--bg:var(--g-c-bg-2, bg-2)"
    class="gp-bg-g-bg-2 gp-flex-1 gp-text-center gp-min-w-max !gp-max-w-max gp-g-s-medium">
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      id="second"
        class="gmOwR35HA5 "
        style="--mb:0px;--c:var(--g-c-text-2, text-2)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-countdown-number gp-g-subheading-2"
          style="--w:100%;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;overflow:hidden"
        >00</div>
      </div>
    </div>
    </gp-text>
    
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmOwR35HA5-text">
    <div
      
        class="gmOwR35HA5 "
        style="--c:var(--g-c-text-1, text-1)"
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-2"
          style="--w:100%;--c:var(--g-c-text-1, text-1);word-break:break-word;overflow:hidden"
        >{{ section.settings.ggmOwR35HA5_secondLabel }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    
      </div>
     </gp-countdown-timer>
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-countdown.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:center"
      class="gPnVj5uYqk gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="goGCUjIHky ">
      
  <style>
    .gp-coupon[data-active="true"] .goGCUjIHky::before {
      
    }
    .gp-coupon[data-active="true"] .goGCUjIHky {
      
    }
    .gp-coupon[data-active="true"] .goGCUjIHky {
      box-shadow: none
    }

  </style>
    <gp-coupon data-id="goGCUjIHky" data-active="false" gp-setting='{"defaultLabel":"Copy","clickedLabel":"Copied!","copyContent":"GEMPAGES30","uid":"goGCUjIHky"}' class="gp-coupon [&_button]:data-[active=true]:!gp-bg-[#00A042]">
    
  <gp-button >
  <div
    style="--ta:left"
    
  >
    <style>
    .goGCUjIHky.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .goGCUjIHky:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .goGCUjIHky:hover .gp-button-icon {
      color: undefined;
    }

     .goGCUjIHky .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .goGCUjIHky:hover .gp-button-price {
      color: undefined;
    }

    .goGCUjIHky .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .goGCUjIHky .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .goGCUjIHky:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="goGCUjIHky-interaction" aria-label="Copy"
      
      data-state="idle"
      class="goGCUjIHky gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-bg-g-brand gp-text-g-text-3 gp-g-paragraph-1"
      style="--bg:var(--g-c-brand, brand);--hvr-bg:#4D4D4D;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3)"
    >
      
    <div
    class="gp-inline-flex">
    <span
        class="gp-inline-flex gp-button-icon gp-transition-colors gp-duration-300 gp-shrink-0 gp-items-center gp-justify-center group-data-[state=loading]/button:gp-invisible gp-z-1 [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
        style="--mr:8px;--height-desktop:1em;--height-tablet:1em;--height-mobile:1em"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" fill="currentColor" data-id="508414808601461096"><path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M216,32H88a8,8,0,0,0-8,8V80H40a8,8,0,0,0-8,8V216a8,8,0,0,0,8,8H168a8,8,0,0,0,8-8V176h40a8,8,0,0,0,8-8V40A8,8,0,0,0,216,32Zm-8,128H176V88a8,8,0,0,0-8-8H96V48H208Z"></path></svg></span>
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggoGCUjIHky_defaultLabel }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>
</gp-coupon>
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-coupon.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>