

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-575067774049584240.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-575067774049584240.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-575067774049584240.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-575067774049584240.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-575067774049584240.gps.gpsil [style*="--hvr-bgc:"]:hover{background-color:var(--hvr-bgc)}.gps-575067774049584240.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-575067774049584240.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-575067774049584240.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-575067774049584240.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-575067774049584240.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-575067774049584240.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-575067774049584240.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-575067774049584240.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-575067774049584240.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-575067774049584240.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-575067774049584240.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-575067774049584240.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-575067774049584240.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-575067774049584240.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-575067774049584240.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-575067774049584240.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-575067774049584240.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-575067774049584240.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-575067774049584240.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-575067774049584240.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-575067774049584240.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-575067774049584240.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-575067774049584240.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-575067774049584240.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-575067774049584240.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-575067774049584240.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-575067774049584240.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-575067774049584240.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-575067774049584240.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-575067774049584240.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-575067774049584240.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-575067774049584240.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-575067774049584240.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-575067774049584240.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-575067774049584240.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-575067774049584240.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-575067774049584240.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-575067774049584240.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-575067774049584240.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-575067774049584240.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-575067774049584240.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-575067774049584240.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-575067774049584240.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-575067774049584240.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-575067774049584240.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-575067774049584240.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-575067774049584240.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-575067774049584240.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-575067774049584240.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-575067774049584240.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-575067774049584240.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-575067774049584240.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-575067774049584240.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-575067774049584240.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-575067774049584240.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-575067774049584240.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-575067774049584240.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-575067774049584240.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-575067774049584240.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-575067774049584240.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-575067774049584240.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-575067774049584240.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-575067774049584240.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-575067774049584240.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-575067774049584240.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-575067774049584240.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-575067774049584240.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-575067774049584240.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-575067774049584240.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-575067774049584240 .gp-g-heading-1{font-family:var(--g-h1-ff);font-size:var(--g-h1-size);font-style:var(--g-h1-fs);font-weight:var(--g-h1-weight);letter-spacing:var(--g-h1-ls);line-height:var(--g-h1-lh)}.gps-575067774049584240 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-575067774049584240 .gp-relative{position:relative}.gps-575067774049584240 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-575067774049584240 .gp-mb-0{margin-bottom:0}.gps-575067774049584240 .gp-block{display:block}.gps-575067774049584240 .gp-flex{display:flex}.gps-575067774049584240 .gp-inline-flex{display:inline-flex}.gps-575067774049584240 .gp-grid{display:grid}.gps-575067774049584240 .\!gp-hidden{display:none!important}.gps-575067774049584240 .gp-hidden{display:none}.gps-575067774049584240 .gp-w-full{width:100%}.gps-575067774049584240 .gp-min-w-0{min-width:0}.gps-575067774049584240 .gp-max-w-full{max-width:100%}.gps-575067774049584240 .gp-flex-1{flex:1 1 0%}.gps-575067774049584240 .gp-flex-none{flex:none}.gps-575067774049584240 .gp-cursor-pointer{cursor:pointer}.gps-575067774049584240 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-575067774049584240 .gp-flex-row{flex-direction:row}.gps-575067774049584240 .gp-flex-col{flex-direction:column}.gps-575067774049584240 .gp-flex-wrap{flex-wrap:wrap}.gps-575067774049584240 .gp-items-center{align-items:center}.gps-575067774049584240 .gp-justify-start{justify-content:flex-start}.gps-575067774049584240 .gp-justify-center{justify-content:center}.gps-575067774049584240 .gp-gap-y-0{row-gap:0}.gps-575067774049584240 .gp-overflow-hidden{overflow:hidden}.gps-575067774049584240 .gp-whitespace-nowrap{white-space:nowrap}.gps-575067774049584240 .gp-p-4{padding:16px}.gps-575067774049584240 .gp-px-4{padding-left:16px;padding-right:16px}.gps-575067774049584240 .gp-py-2{padding-bottom:8px;padding-top:8px}.gps-575067774049584240 .gp-pb-0{padding-bottom:0}.gps-575067774049584240 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575067774049584240 .gp-duration-200{transition-duration:.2s}.gps-575067774049584240 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-575067774049584240 .tablet\:gp-block{display:block}.gps-575067774049584240 .tablet\:\!gp-hidden{display:none!important}.gps-575067774049584240 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-575067774049584240 .mobile\:gp-block{display:block}.gps-575067774049584240 .mobile\:\!gp-hidden{display:none!important}.gps-575067774049584240 .mobile\:gp-hidden{display:none}}.gps-575067774049584240 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-575067774049584240 .\[\&_p\]\:gp-inline p{display:inline}.gps-575067774049584240 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-575067774049584240 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gg9JCEMfui" data-id="gg9JCEMfui"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gg9JCEMfui gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gxTDhz3ep9 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gxhkJ3tLo5" data-id="gxhkJ3tLo5"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gxhkJ3tLo5 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="grM8A3t_Tm gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKwhceqYAf">
    <div
      parentTag="Col"
        class="gKwhceqYAf "
        style="--ta:left;--mt:-50px;--mb:40px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-heading-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--weight:bold;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggKwhceqYAf_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    <gp-tab data-id="g3PNRYW1w-" gp-data='{"setting":{"borderTab":{"active":{"border":"solid","color":"#2352E7","isCustom":true,"width":"0px 0px 2px 0px"},"hover":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#2352E7","isCustom":true,"isLink":true,"width":"0px 0px 2px 0px"},"normal":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#EEEEEE","isCustom":true,"isLink":true,"width":"0px 0px 1px 0px"}},"childItem":["<p>Machine Parameters</p>","<p>Sensors</p>","<p>Software</p>","<p>Electrical Hardware</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"rgba(245, 245, 245, 0)","normal":"transparent"},"labelColor":{"active":"#2352E7","hover":"#2352E7","normal":"#242424"},"labelTypo":{"custom":{"desktop":{"fontSize":"19px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"130%"},"mobile":{"fontSize":"17px","letterSpacing":"0px","lineHeight":"150%"},"tablet":{"fontSize":"19px","lineHeight":"130%"}}},"labelTypoV2":{"type":"paragraph-1","custom":{"fontSize":{"desktop":"22px","mobile":"17px","tablet":"19px"},"fontStyle":"normal","fontWeight":"700","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}}},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"center"},"panelFullWidth":{"desktop":false,"mobile":true},"panelWidth":{"desktop":"680px","mobile":"15%"},"position":{"0":"t","1":"o","2":"p","desktop":"top"},"translate":"childItem"},"builderProps":{"uid":"g3PNRYW1w-","builderData":{"advanced":{"border":{"desktop":{"normal":{"border":"none","borderType":"none","borderWidth":{},"color":"transparent","isCustom":false,"width":"0px"}}},"boxShadow":{"desktop":{"normal":{"angle":90,"blur":"4px","color":"rgba(18, 18, 18, 0.12)","distance":"2px","spread":"0px","type":"shadow-1"}}},"d":{"desktop":true,"mobile":true,"tablet":true},"hasBoxShadow":{"desktop":{"normal":false}},"op":{"desktop":"100%"},"rounded":{"desktop":{"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}}},"spacing-setting":{"desktop":{"margin":{"bottom":0},"padding":""},"mobile":{"margin":"","padding":""},"tablet":{"margin":"","padding":""}}},"childrens":["gDA4X9NcU9","gl2uewg8KL","gR9KqVaujp","gAeVeuZLhe"],"label":"Tab","settings":{"borderTab":{"active":{"border":"solid","color":"#2352E7","isCustom":true,"width":"0px 0px 2px 0px"},"hover":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#2352E7","isCustom":true,"isLink":true,"width":"0px 0px 2px 0px"},"normal":{"border":"solid","borderLabel":"Style 1","borderType":"style-1","color":"#EEEEEE","isCustom":true,"isLink":true,"width":"0px 0px 1px 0px"}},"childItem":["<p>Machine Parameters</p>","<p>Sensors</p>","<p>Software</p>","<p>Electrical Hardware</p>"],"labelAlign":{"desktop":"center"},"labelBgColor":{"active":"rgba(245, 245, 245, 0)","normal":"transparent"},"labelColor":{"active":"#2352E7","hover":"#2352E7","normal":"#242424"},"labelTypo":{"custom":{"desktop":{"fontSize":"19px","fontStyle":"normal","fontWeight":"700","letterSpacing":"0px","lineHeight":"130%"},"mobile":{"fontSize":"17px","letterSpacing":"0px","lineHeight":"150%"},"tablet":{"fontSize":"19px","lineHeight":"130%"}}},"labelTypoV2":{"type":"paragraph-1","custom":{"fontSize":{"desktop":"22px","mobile":"17px","tablet":"19px"},"fontStyle":"normal","fontWeight":"700","letterSpacing":"normal","lineHeight":{"desktop":"130%","mobile":"130%","tablet":"130%"}}},"labelWidth":{"desktop":"200px"},"panelAlign":{"desktop":"center"},"panelFullWidth":{"desktop":false,"mobile":true},"panelWidth":{"desktop":"680px","mobile":"15%"},"position":{"0":"t","1":"o","2":"p","desktop":"top"},"translate":"childItem"},"styles":{},"tag":"Tabs","uid":"g3PNRYW1w-","type":"component"}}}'>
      <div
        
        data-id=""
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
        class="gp-flex g3PNRYW1w-"
      >
        <style>
            .g3PNRYW1w- .gp-navs-tab.left p,
            .g3PNRYW1w- .gp-navs-tab.right p {
              word-wrap: break-word;
              white-space: break-spaces;
            }
            .g3PNRYW1w- .wrap-width_full_mobile_top_true { width: 100%; }

        </style>
        <div
          class="gp-flex gp-w-full  gp-flex-col"
        >
          <div
            class="gp-flex"
            style="--jc:center"
          >
            <ul
              class="gp-tab-header-list gp-flex gp-flex-wrap  0:gp-flex-row 1:gp-flex-row 2:gp-flex-row gp-flex-row"
              style="--maxw:680px;--maxw-tablet:680px;--maxw-mobile:100%"
            >
              
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="g3PNRYW1w-"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg3PNRYW1w-_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="g3PNRYW1w-"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg3PNRYW1w-_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="g3PNRYW1w-"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg3PNRYW1w-_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
                  <li
                    class="gp-navs-tab gp-flex gp-flex-1 gp-cursor-pointer gp-py-2 gp-px-4"
                    aria-hidden
                    style="--bs:unset, --hvr-bs:unset, --hvr-bgc:unset, --hvr-c:unset,--hvr-bw:unset, --hvr-bc:unset"
                    key="g3PNRYW1w-"
                  >
                    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-cursor-pointer gp-overflow-hidden gp-whitespace-nowrap"
          style="--w:100%;--ta:center;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:22px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg3PNRYW1w-_childItem_3 }}</div>
      </div>
    </div>
    </gp-text>
    
                  </li>
                
            </ul>
          </div>
          <div
            class="gp-flex gp-flex-1 gp-min-w-0"
          >
            <div
              class="gp-tab-item-container gp-p-4 gp-pb-0"
              key="g3PNRYW1w-"
              style="--w:100%"
            >
            
  <div
    
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-g3PNRYW1w-"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="gHurTasYy4 ">
      
    <div
    data-id="gHurTasYy4"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gz3P4QT-GS" data-id="gz3P4QT-GS"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gz3P4QT-GS gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gekFSCSuZl gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gtwTXzgUgL">
    <div
      parentTag="Col"
        class="gtwTXzgUgL "
        style="--ta:left;--mb:-10px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggtwTXzgUgL_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ggyVDjlWwD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g5In2qp59H">
    <div
      parentTag="Col"
        class="g5In2qp59H "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg5In2qp59H_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gSTyZd1_Zb gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmS_A_w_-9">
    <div
      parentTag="Col"
        class="gmS_A_w_-9 "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggmS_A_w_-9_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g_aDFBggUf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gfjo19R3r8">
    <div
      parentTag="Col"
        class="gfjo19R3r8 "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggfjo19R3r8_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="g4EeTyoCZf" data-id="g4EeTyoCZf"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g4EeTyoCZf gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gfHrCsC1lB gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gxLQ0fJ2ch">
    <div
      parentTag="Col"
        class="gxLQ0fJ2ch "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggxLQ0fJ2ch_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gl_4bwjZOx gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g-rJyURxtU">
    <div
      parentTag="Col"
        class="g-rJyURxtU "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg-rJyURxtU_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gFkuOWZymm gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="guVkruRfbg">
    <div
      parentTag="Col"
        class="guVkruRfbg "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gguVkruRfbg_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gFr4uLmGT2 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIBHau-F_e">
    <div
      parentTag="Col"
        class="gIBHau-F_e "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggIBHau-F_e_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gJqDbbi10d" data-id="gJqDbbi10d"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gJqDbbi10d gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gaWMsk6_G7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gDIJBYze65">
    <div
      parentTag="Col"
        class="gDIJBYze65 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggDIJBYze65_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g5YSsPgkwl gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gHLAapepzd">
    <div
      parentTag="Col"
        class="gHLAapepzd "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggHLAapepzd_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gng-nYav6_ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g1LopVfP1Q">
    <div
      parentTag="Col"
        class="g1LopVfP1Q "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg1LopVfP1Q_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g4B_RQ-NR5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gzdz-GvCpd">
    <div
      parentTag="Col"
        class="gzdz-GvCpd "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggzdz-GvCpd_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gxV6Ip2WoS" data-id="gxV6Ip2WoS"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gxV6Ip2WoS gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gF9Xa6rvUf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gEQjHAmoxt">
    <div
      parentTag="Col"
        class="gEQjHAmoxt "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggEQjHAmoxt_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gjjJuXKwxu gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g7jxWmxn71">
    <div
      parentTag="Col"
        class="g7jxWmxn71 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg7jxWmxn71_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g607Nz9ReV gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gegBy_JFxj">
    <div
      parentTag="Col"
        class="gegBy_JFxj "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggegBy_JFxj_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gFKUiqHng0 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gohXTdCo9E">
    <div
      parentTag="Col"
        class="gohXTdCo9E "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggohXTdCo9E_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="geAkkVkSXO" data-id="geAkkVkSXO"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="geAkkVkSXO gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gRaGNp5uQY gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKbEBKGsxL">
    <div
      parentTag="Col"
        class="gKbEBKGsxL "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggKbEBKGsxL_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gypKAUeMpa gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gaTCxvHJW6">
    <div
      parentTag="Col"
        class="gaTCxvHJW6 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggaTCxvHJW6_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gLhuIs1ZLc gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="geFwHOzcRj">
    <div
      parentTag="Col"
        class="geFwHOzcRj "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggeFwHOzcRj_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gPCvQttt4N gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="goG79DAM4S">
    <div
      parentTag="Col"
        class="goG79DAM4S "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggoG79DAM4S_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gPeaIEden7" data-id="gPeaIEden7"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gPeaIEden7 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="glVdCFty_P gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g9llV2xQcA">
    <div
      parentTag="Col"
        class="g9llV2xQcA "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg9llV2xQcA_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gcAKJzgjRW gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gnytiRCJGE">
    <div
      parentTag="Col"
        class="gnytiRCJGE "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggnytiRCJGE_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gMSHhaW99q gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g8xRMkeEzp">
    <div
      parentTag="Col"
        class="g8xRMkeEzp "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg8xRMkeEzp_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gvUoHF3i5I gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gLcdKdlJYm">
    <div
      parentTag="Col"
        class="gLcdKdlJYm "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggLcdKdlJYm_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gLtOZIMD1s" data-id="gLtOZIMD1s"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gLtOZIMD1s gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g-6QfIX92Y gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIJ64yFW9h">
    <div
      parentTag="Col"
        class="gIJ64yFW9h "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggIJ64yFW9h_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gK4CEwecCh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gtt3ZwKYOW">
    <div
      parentTag="Col"
        class="gtt3ZwKYOW "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggtt3ZwKYOW_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gg_K8gsqLo gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmBaBUsne4">
    <div
      parentTag="Col"
        class="gmBaBUsne4 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggmBaBUsne4_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gUtlkgksFN gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gRBKOxUhBK">
    <div
      parentTag="Col"
        class="gRBKOxUhBK "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggRBKOxUhBK_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gPozpdXyKe" data-id="gPozpdXyKe"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gPozpdXyKe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gPcLDN-DaI gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gaVQnRRJUn">
    <div
      parentTag="Col"
        class="gaVQnRRJUn "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggaVQnRRJUn_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gwA4FPCn4j gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gMQoi6PsPB">
    <div
      parentTag="Col"
        class="gMQoi6PsPB "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggMQoi6PsPB_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="glgoyWuktT gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gS3OrytlPQ">
    <div
      parentTag="Col"
        class="gS3OrytlPQ "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggS3OrytlPQ_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gUo8D4Vj7v gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="groHTrfLuC">
    <div
      parentTag="Col"
        class="groHTrfLuC "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggroHTrfLuC_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="g209BLJLjR" data-id="g209BLJLjR"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g209BLJLjR gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gi5YBmFx0p gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdwqbEhj4e">
    <div
      parentTag="Col"
        class="gdwqbEhj4e "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggdwqbEhj4e_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gK5EGvTTXx gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gg2E_h7BlU">
    <div
      parentTag="Col"
        class="gg2E_h7BlU "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggg2E_h7BlU_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gZdVo2r7Wl gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gkqW0itdmu">
    <div
      parentTag="Col"
        class="gkqW0itdmu "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggkqW0itdmu_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g89BRqF5ql gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gooEISq7HN">
    <div
      parentTag="Col"
        class="gooEISq7HN "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggooEISq7HN_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gJadaWpisK" data-id="gJadaWpisK"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gJadaWpisK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gB8i4rj8SV gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="geGI8moi1S">
    <div
      parentTag="Col"
        class="geGI8moi1S "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggeGI8moi1S_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ggJ4bxRsYa gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gS6nf43agN">
    <div
      parentTag="Col"
        class="gS6nf43agN "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggS6nf43agN_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g-ChKCgvs3 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gqoVKC6HcH">
    <div
      parentTag="Col"
        class="gqoVKC6HcH "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggqoVKC6HcH_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gq-vP7nVUX gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKkvruNWTI">
    <div
      parentTag="Col"
        class="gKkvruNWTI "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggKkvruNWTI_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="g7lzTeR_RU" data-id="g7lzTeR_RU"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g7lzTeR_RU gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gd0vkbW0yA gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gN2ITSVqm5">
    <div
      parentTag="Col"
        class="gN2ITSVqm5 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggN2ITSVqm5_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g4ZNUyXV3A gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gMbruCV6d0">
    <div
      parentTag="Col"
        class="gMbruCV6d0 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggMbruCV6d0_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gYrnD3nzbs gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gzzI5dMHyp">
    <div
      parentTag="Col"
        class="gzzI5dMHyp "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggzzI5dMHyp_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gMN0ALfvYh gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOHjvy3CK1">
    <div
      parentTag="Col"
        class="gOHjvy3CK1 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggOHjvy3CK1_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gMUQ809NEs" data-id="gMUQ809NEs"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gMUQ809NEs gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gIOnrdeyrD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g29P5A1jfI">
    <div
      parentTag="Col"
        class="g29P5A1jfI "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg29P5A1jfI_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ghGbMgLx2p gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="geyW2mIhkL">
    <div
      parentTag="Col"
        class="geyW2mIhkL "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggeyW2mIhkL_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g9tBNZgc8I gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gghGzln34v">
    <div
      parentTag="Col"
        class="gghGzln34v "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggghGzln34v_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gSpK0UkUXd gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="grZHhyQqRW">
    <div
      parentTag="Col"
        class="grZHhyQqRW "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggrZHhyQqRW_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gSq3LVJyQe" data-id="gSq3LVJyQe"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gSq3LVJyQe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g7Bpzo57B7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gVwJT2oT4q">
    <div
      parentTag="Col"
        class="gVwJT2oT4q "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggVwJT2oT4q_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gKQGm9UV2L gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="guJI26XOY9">
    <div
      parentTag="Col"
        class="guJI26XOY9 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gguJI26XOY9_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g3Uz1b2fb- gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gm0eUaNZTq">
    <div
      parentTag="Col"
        class="gm0eUaNZTq "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggm0eUaNZTq_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gfDxRE1HRD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gcZRySB2Xf">
    <div
      parentTag="Col"
        class="gcZRySB2Xf "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggcZRySB2Xf_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gz3iTS6dKD" data-id="gz3iTS6dKD"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gz3iTS6dKD gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g6RCPDWHCk gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZNJqn-hNk">
    <div
      parentTag="Col"
        class="gZNJqn-hNk "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggZNJqn-hNk_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gPeZdJjhHf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gk9nHSSHGI">
    <div
      parentTag="Col"
        class="gk9nHSSHGI "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggk9nHSSHGI_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gT97YYLhuW gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gkoGlBLE1L">
    <div
      parentTag="Col"
        class="gkoGlBLE1L "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggkoGlBLE1L_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gneMSbywip gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gAdaWYMT1s">
    <div
      parentTag="Col"
        class="gAdaWYMT1s "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggAdaWYMT1s_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  </div>
  
  <div
    
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-g3PNRYW1w-"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="gZNAUXUUVm ">
      
    <div
    data-id="gZNAUXUUVm"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gBCCt-lB0Z" data-id="gBCCt-lB0Z"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gBCCt-lB0Z gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gotJ3r9XmF gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gLABDHbNR2">
    <div
      parentTag="Col"
        class="gLABDHbNR2 "
        style="--ta:left;--mb:-20px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggLABDHbNR2_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gdrNeHMLrt gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g5ihCRVWei">
    <div
      parentTag="Col"
        class="g5ihCRVWei "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg5ihCRVWei_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gU5Ah2wZ1G gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g1sJK8U1G0">
    <div
      parentTag="Col"
        class="g1sJK8U1G0 "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg1sJK8U1G0_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g2geSUVUdx gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdgvGg9pwH">
    <div
      parentTag="Col"
        class="gdgvGg9pwH "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggdgvGg9pwH_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gy6YXP56BI" data-id="gy6YXP56BI"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gy6YXP56BI gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="glOYbnx0FX gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gh14_EJ_lI">
    <div
      parentTag="Col"
        class="gh14_EJ_lI "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggh14_EJ_lI_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gmnTI6Rqzo gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gMe9qFrpYD">
    <div
      parentTag="Col"
        class="gMe9qFrpYD "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggMe9qFrpYD_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ggquy6AELn gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gi4hFaBB1z">
    <div
      parentTag="Col"
        class="gi4hFaBB1z "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggi4hFaBB1z_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gQ1WtKhYbJ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g8Kl-S3bzP">
    <div
      parentTag="Col"
        class="g8Kl-S3bzP "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg8Kl-S3bzP_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gS9tfMCrI-" data-id="gS9tfMCrI-"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gS9tfMCrI- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gNmk94Fqkb gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="get-p65Nvq">
    <div
      parentTag="Col"
        class="get-p65Nvq "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gget-p65Nvq_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g0gMaviofD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g40MdB5XMx">
    <div
      parentTag="Col"
        class="g40MdB5XMx "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg40MdB5XMx_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g57BahH_8o gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="guQFUyIkgc">
    <div
      parentTag="Col"
        class="guQFUyIkgc "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gguQFUyIkgc_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ga-2Rv_mKk gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gPHmkkoPYa">
    <div
      parentTag="Col"
        class="gPHmkkoPYa "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggPHmkkoPYa_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gQX0boANct" data-id="gQX0boANct"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gQX0boANct gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gE_dvL1l4O gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g5dfsmDkI7">
    <div
      parentTag="Col"
        class="g5dfsmDkI7 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg5dfsmDkI7_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ge7Tb5AYcM gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gU8JfglGY0">
    <div
      parentTag="Col"
        class="gU8JfglGY0 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggU8JfglGY0_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="ge0C9sV-2Q gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g71s_C8UWw">
    <div
      parentTag="Col"
        class="g71s_C8UWw "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg71s_C8UWw_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g5usZt453e gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gGocv-UTwX">
    <div
      parentTag="Col"
        class="gGocv-UTwX "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggGocv-UTwX_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gfZEa74VR-" data-id="gfZEa74VR-"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gfZEa74VR- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g_uMGwY-0D gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSm8EwmNPw">
    <div
      parentTag="Col"
        class="gSm8EwmNPw "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggSm8EwmNPw_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gUZWomVqck gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gLXwGf8yhD">
    <div
      parentTag="Col"
        class="gLXwGf8yhD "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggLXwGf8yhD_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start;--d-mobile:none"
      class="gr3KhV_j-F gp-relative gp-flex gp-flex-col"
    >
      
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start;--d-mobile:none"
      class="gIUrRQ5oo8 gp-relative gp-flex gp-flex-col"
    >
      
    </div>
    </div>
   
    
  </div>
  
  <div
    
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-g3PNRYW1w-"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="g40veVf-eC ">
      
    <div
    data-id="g40veVf-eC"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gEc6i4NfJZ" data-id="gEc6i4NfJZ"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gEc6i4NfJZ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g5MH9Fk040 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="giPiq6GnfF">
    <div
      parentTag="Col"
        class="giPiq6GnfF "
        style="--ta:left;--mb:-20px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggiPiq6GnfF_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gpIxW3zDy0 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gUZJfYrs0U">
    <div
      parentTag="Col"
        class="gUZJfYrs0U "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggUZJfYrs0U_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gVzLRzesu_ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="grdZ1XCkjn">
    <div
      parentTag="Col"
        class="grdZ1XCkjn "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggrdZ1XCkjn_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gMOPG6MzkF gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gDbsowOjRR">
    <div
      parentTag="Col"
        class="gDbsowOjRR "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggDbsowOjRR_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gf55JANvWn" data-id="gf55JANvWn"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gf55JANvWn gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gVKs7ceR42 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g6Pk--IsbZ">
    <div
      parentTag="Col"
        class="g6Pk--IsbZ "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg6Pk--IsbZ_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gy-_n2g6w3 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gEEbor2UP9">
    <div
      parentTag="Col"
        class="gEEbor2UP9 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggEEbor2UP9_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="go8NXkBMSJ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggEz6GEL9J">
    <div
      parentTag="Col"
        class="ggEz6GEL9J "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gggEz6GEL9J_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g0RhaU0f6s gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gkFXlmsn7s">
    <div
      parentTag="Col"
        class="gkFXlmsn7s "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggkFXlmsn7s_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="g4UmHdTyb4" data-id="g4UmHdTyb4"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g4UmHdTyb4 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gw0EiNZzva gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gm9MYpVak4">
    <div
      parentTag="Col"
        class="gm9MYpVak4 "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:525%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggm9MYpVak4_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="guXEsTLSPS gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gQq3Gxjx1X">
    <div
      parentTag="Col"
        class="gQq3Gxjx1X "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:525%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggQq3Gxjx1X_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gy0MJPqFPX gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g6T5TH0uxu">
    <div
      parentTag="Col"
        class="g6T5TH0uxu "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:525%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg6T5TH0uxu_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gE2KWOVUjR gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gXspPGtY7t">
    <div
      parentTag="Col"
        class="gXspPGtY7t "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggXspPGtY7t_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  </div>
  
  <div
    
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
    class="gp-tab-item gp-child-item-g3PNRYW1w-"
  >
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:var(--g-s-2xl)" class="gp7hxrYIb- ">
      
    <div
    data-id="gp7hxrYIb-"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--t:gp-rotate(0deg);--bc:rgba(125, 125, 125, 0)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100px;--w-tablet:100px;--w-mobile:100px;--bc:rgba(125, 125, 125, 0);--bs:solid;--bbw:1pxpx;--bbw-tablet:undefinedpx;--bbw-mobile:undefinedpx;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
       
      
    <div
      parentTag="TabItem" id="gsL1Sy8uhj" data-id="gsL1Sy8uhj"
        style="--mb:var(--g-s-2xl);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gsL1Sy8uhj gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="grxQWKRKQm gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gDUjP3YK38">
    <div
      parentTag="Col"
        class="gDUjP3YK38 "
        style="--ta:left;--mb:-20px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:21px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggDUjP3YK38_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gVfyt48rYl gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_C3sKgWOz">
    <div
      parentTag="Col"
        class="g_C3sKgWOz "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg_C3sKgWOz_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g_eEz9z6Rj gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjnx9SxWbR">
    <div
      parentTag="Col"
        class="gjnx9SxWbR "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggjnx9SxWbR_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gNKnjm6O7j gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOjn_apI46">
    <div
      parentTag="Col"
        class="gOjn_apI46 "
        style="--ta:left;--mb:-100px;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--size:24px;--size-tablet:16px;--size-mobile:14px;--lh:100%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#424242;word-break:break-word;--bgc:rgba(246, 246, 246, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggOjn_apI46_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gamrBGA4GF" data-id="gamrBGA4GF"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gamrBGA4GF gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gNt2JSxt66 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gcSdDcqLBD">
    <div
      parentTag="Col"
        class="gcSdDcqLBD "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggcSdDcqLBD_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g2Zac6mXHs gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggaCxEmOx_">
    <div
      parentTag="Col"
        class="ggaCxEmOx_ "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gggaCxEmOx__text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g3OAZKFdwM gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmGmAhA2uj">
    <div
      parentTag="Col"
        class="gmGmAhA2uj "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggmGmAhA2uj_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gOCIJ-D5iP gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZO_UQcR-H">
    <div
      parentTag="Col"
        class="gZO_UQcR-H "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggZO_UQcR-H_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="gkVUr1BVcc" data-id="gkVUr1BVcc"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gkVUr1BVcc gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gO7f1pnYT_ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g9fql8z26k">
    <div
      parentTag="Col"
        class="g9fql8z26k "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.gg9fql8z26k_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gDhlnk-Vix gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gB2TjAILZO">
    <div
      parentTag="Col"
        class="gB2TjAILZO "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggB2TjAILZO_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gNRkkGFzTt gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjfUTSzmAu">
    <div
      parentTag="Col"
        class="gjfUTSzmAu "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggjfUTSzmAu_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="goF2NUVoJw gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOQc7ZFCgj">
    <div
      parentTag="Col"
        class="gOQc7ZFCgj "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggOQc7ZFCgj_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="TabItem" id="ge4bCnR1tK" data-id="ge4bCnR1tK"
        style="--mb:2px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:2px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--h:auto;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="ge4bCnR1tK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gpN1r3u0ir gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gfut8Kzq5b">
    <div
      parentTag="Col"
        class="gfut8Kzq5b "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggfut8Kzq5b_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gy5wSSX_Z7 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gQQnVpm8g-">
    <div
      parentTag="Col"
        class="gQQnVpm8g- "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--ls:Auto;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:175%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggQQnVpm8g-_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g_vIN7jgkp gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gqQJMvUaBV">
    <div
      parentTag="Col"
        class="gqQJMvUaBV "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggqQJMvUaBV_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gVbh7kBOpX gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gp8CzR0o0k">
    <div
      parentTag="Col"
        class="gp8CzR0o0k "
        style="--ta:left;--pt:0px;--pb:0px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:350%;--lh-tablet:180%;--lh-mobile:180%;--c:#424242;word-break:break-word;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;overflow:hidden"
        >{{ section.settings.ggp8CzR0o0k_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  </div>
  
            </div>
          </div>
        </div>
      </div>
    </gp-tab>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-tab.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 4",
    "tag": "section",
    "class": "gps-575067774049584240 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=575053851124565104&sectionId=575067774049584240)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggKwhceqYAf_text","label":"ggKwhceqYAf_text","default":"IR3 V2 3D Prnter Specifications"},{"type":"html","id":"gg3PNRYW1w-_childItem_0","label":"gg3PNRYW1w-_childItem_0","default":"<p>Machine Parameters</p>"},{"type":"html","id":"gg3PNRYW1w-_childItem_1","label":"gg3PNRYW1w-_childItem_1","default":"<p>Sensors</p>"},{"type":"html","id":"gg3PNRYW1w-_childItem_2","label":"gg3PNRYW1w-_childItem_2","default":"<p>Software</p>"},{"type":"html","id":"gg3PNRYW1w-_childItem_3","label":"gg3PNRYW1w-_childItem_3","default":"<p>Electrical Hardware</p>"},{"type":"html","id":"ggtwTXzgUgL_text","label":"ggtwTXzgUgL_text","default":"Parameter"},{"type":"html","id":"gg5In2qp59H_text","label":"gg5In2qp59H_text","default":"Specification"},{"type":"html","id":"ggmS_A_w_-9_text","label":"ggmS_A_w_-9_text","default":"Parameter"},{"type":"html","id":"ggfjo19R3r8_text","label":"ggfjo19R3r8_text","default":"Specification"},{"type":"html","id":"ggxLQ0fJ2ch_text","label":"ggxLQ0fJ2ch_text","default":"Printing Technology"},{"type":"html","id":"gg-rJyURxtU_text","label":"gg-rJyURxtU_text","default":"FDM&nbsp;"},{"type":"html","id":"gguVkruRfbg_text","label":"gguVkruRfbg_text","default":"Layer Thickness&nbsp;"},{"type":"html","id":"ggIBHau-F_e_text","label":"ggIBHau-F_e_text","default":"0.1-0.3mm"},{"type":"html","id":"ggDIJBYze65_text","label":"ggDIJBYze65_text","default":"Machine Structure"},{"type":"html","id":"ggHLAapepzd_text","label":"ggHLAapepzd_text","default":"Full metal frame&nbsp;"},{"type":"html","id":"gg1LopVfP1Q_text","label":"gg1LopVfP1Q_text","default":"Print Speed"},{"type":"html","id":"ggzdz-GvCpd_text","label":"ggzdz-GvCpd_text","default":"≤400mm/s"},{"type":"html","id":"ggEQjHAmoxt_text","label":"ggEQjHAmoxt_text","default":"Motion Structure"},{"type":"html","id":"gg7jxWmxn71_text","label":"gg7jxWmxn71_text","default":"CoreXY"},{"type":"html","id":"ggegBy_JFxj_text","label":"ggegBy_JFxj_text","default":"Print Acceleration"},{"type":"html","id":"ggohXTdCo9E_text","label":"ggohXTdCo9E_text","default":"≤20000mm/s²"},{"type":"html","id":"ggKbEBKGsxL_text","label":"ggKbEBKGsxL_text","default":"Filament Diameter"},{"type":"html","id":"ggaTCxvHJW6_text","label":"ggaTCxvHJW6_text","default":"1.75mm"},{"type":"html","id":"ggeFwHOzcRj_text","label":"ggeFwHOzcRj_text","default":"Maximum Nozzle Temperature"},{"type":"html","id":"ggoG79DAM4S_text","label":"ggoG79DAM4S_text","default":"300°C"},{"type":"html","id":"gg9llV2xQcA_text","label":"gg9llV2xQcA_text","default":"Motor Type"},{"type":"html","id":"ggnytiRCJGE_text","label":"ggnytiRCJGE_text","default":"5:1 Dual gear reduction extruder motor"},{"type":"html","id":"gg8xRMkeEzp_text","label":"gg8xRMkeEzp_text","default":"Maximum Heated &nbsp;Bed Temperature"},{"type":"html","id":"ggLcdKdlJYm_text","label":"ggLcdKdlJYm_text","default":"90°C"},{"type":"html","id":"ggIJ64yFW9h_text","label":"ggIJ64yFW9h_text","default":"Nozzle Material"},{"type":"html","id":"ggtt3ZwKYOW_text","label":"ggtt3ZwKYOW_text","default":"Hardened steel"},{"type":"html","id":"ggmBaBUsne4_text","label":"ggmBaBUsne4_text","default":"Nozzle Heating Time"},{"type":"html","id":"ggRBKOxUhBK_text","label":"ggRBKOxUhBK_text","default":"40s"},{"type":"html","id":"ggaVQnRRJUn_text","label":"ggaVQnRRJUn_text","default":"Nozzle Size"},{"type":"html","id":"ggMQoi6PsPB_text","label":"ggMQoi6PsPB_text","default":"Standard 0.4mm"},{"type":"html","id":"ggS3OrytlPQ_text","label":"ggS3OrytlPQ_text","default":"Heated Bed Heating Time"},{"type":"html","id":"ggroHTrfLuC_text","label":"ggroHTrfLuC_text","default":"90s"},{"type":"html","id":"ggdwqbEhj4e_text","label":"ggdwqbEhj4e_text","default":"Print Volume&nbsp;"},{"type":"html","id":"ggg2E_h7BlU_text","label":"ggg2E_h7BlU_text","default":"250×250×∞mm(X*Y*Z)"},{"type":"html","id":"ggkqW0itdmu_text","label":"ggkqW0itdmu_text","default":"Maximum Flow Rate"},{"type":"html","id":"ggooEISq7HN_text","label":"ggooEISq7HN_text","default":"26mm³/s"},{"type":"html","id":"ggeGI8moi1S_text","label":"ggeGI8moi1S_text","default":"Product Dimensions"},{"type":"html","id":"ggS6nf43agN_text","label":"ggS6nf43agN_text","default":"676×436×510mm"},{"type":"html","id":"ggqoVKC6HcH_text","label":"ggqoVKC6HcH_text","default":"Print Platform&nbsp;"},{"type":"html","id":"ggKkvruNWTI_text","label":"ggKkvruNWTI_text","default":"PEI metal build surface"},{"type":"html","id":"ggN2ITSVqm5_text","label":"ggN2ITSVqm5_text","default":"Package Dimensions"},{"type":"html","id":"ggMbruCV6d0_text","label":"ggMbruCV6d0_text","default":"770×510×320mm"},{"type":"html","id":"ggzzI5dMHyp_text","label":"ggzzI5dMHyp_text","default":"Compatible Materials"},{"type":"html","id":"ggOHjvy3CK1_text","label":"ggOHjvy3CK1_text","default":"PLA/PETG/TPU/ ABS/ASA, etc."},{"type":"html","id":"gg29P5A1jfI_text","label":"gg29P5A1jfI_text","default":"Net Weight"},{"type":"html","id":"ggeyW2mIhkL_text","label":"ggeyW2mIhkL_text","default":"16.5kg"},{"type":"html","id":"ggghGzln34v_text","label":"ggghGzln34v_text","default":"Operating Environment &nbsp;Temperature"},{"type":"html","id":"ggrZHhyQqRW_text","label":"ggrZHhyQqRW_text","default":"10-40°C"},{"type":"html","id":"ggVwJT2oT4q_text","label":"ggVwJT2oT4q_text","default":"Gross Weight"},{"type":"html","id":"gguJI26XOY9_text","label":"gguJI26XOY9_text","default":"21kg"},{"type":"html","id":"ggm0eUaNZTq_text","label":"ggm0eUaNZTq_text","default":"Noise Level"},{"type":"html","id":"ggcZRySB2Xf_text","label":"ggcZRySB2Xf_text","default":"54dB"},{"type":"html","id":"ggZNJqn-hNk_text","label":"ggZNJqn-hNk_text","default":"Print Precision"},{"type":"html","id":"ggk9nHSSHGI_text","label":"ggk9nHSSHGI_text","default":"±0.1mm"},{"type":"html","id":"ggkoGlBLE1L_text","label":"ggkoGlBLE1L_text","default":"Print Methods"},{"type":"html","id":"ggAdaWYMT1s_text","label":"ggAdaWYMT1s_text","default":"USB drive/Local network/I nternet network"},{"type":"html","id":"ggLABDHbNR2_text","label":"ggLABDHbNR2_text","default":"Feature"},{"type":"html","id":"gg5ihCRVWei_text","label":"gg5ihCRVWei_text","default":"Support"},{"type":"html","id":"gg1sJK8U1G0_text","label":"gg1sJK8U1G0_text","default":"Feature"},{"type":"html","id":"ggdgvGg9pwH_text","label":"ggdgvGg9pwH_text","default":"Support"},{"type":"html","id":"ggh14_EJ_lI_text","label":"ggh14_EJ_lI_text","default":"Printing Vibration Compensation"},{"type":"html","id":"ggMe9qFrpYD_text","label":"ggMe9qFrpYD_text","default":"Yes"},{"type":"html","id":"ggi4hFaBB1z_text","label":"ggi4hFaBB1z_text","default":"Filament Runout Detection"},{"type":"html","id":"gg8Kl-S3bzP_text","label":"gg8Kl-S3bzP_text","default":"Yes"},{"type":"html","id":"gget-p65Nvq_text","label":"gget-p65Nvq_text","default":"Material Shortage Detection"},{"type":"html","id":"gg40MdB5XMx_text","label":"gg40MdB5XMx_text","default":"Yes"},{"type":"html","id":"gguQFUyIkgc_text","label":"gguQFUyIkgc_text","default":"Clogging Detection"},{"type":"html","id":"ggPHmkkoPYa_text","label":"ggPHmkkoPYa_text","default":"Yes"},{"type":"html","id":"gg5dfsmDkI7_text","label":"gg5dfsmDkI7_text","default":"Auto Leveling"},{"type":"html","id":"ggU8JfglGY0_text","label":"ggU8JfglGY0_text","default":"Yes"},{"type":"html","id":"gg71s_C8UWw_text","label":"gg71s_C8UWw_text","default":"LED Lighting"},{"type":"html","id":"ggGocv-UTwX_text","label":"ggGocv-UTwX_text","default":"Yes"},{"type":"html","id":"ggSm8EwmNPw_text","label":"ggSm8EwmNPw_text","default":"Camera"},{"type":"html","id":"ggLXwGf8yhD_text","label":"ggLXwGf8yhD_text","default":"Yes"},{"type":"html","id":"ggiPiq6GnfF_text","label":"ggiPiq6GnfF_text","default":"Feature"},{"type":"html","id":"ggUZJfYrs0U_text","label":"ggUZJfYrs0U_text","default":"Specification"},{"type":"html","id":"ggrdZ1XCkjn_text","label":"ggrdZ1XCkjn_text","default":"Feature"},{"type":"html","id":"ggDbsowOjRR_text","label":"ggDbsowOjRR_text","default":"Specification"},{"type":"html","id":"gg6Pk--IsbZ_text","label":"gg6Pk--IsbZ_text","default":"Slicing Software"},{"type":"html","id":"ggEEbor2UP9_text","label":"ggEEbor2UP9_text","default":"Ideamaker/ Ideaformer Cura"},{"type":"html","id":"gggEz6GEL9J_text","label":"gggEz6GEL9J_text","default":"Operating Systems"},{"type":"html","id":"ggkFXlmsn7s_text","label":"ggkFXlmsn7s_text","default":"Windows/MacOS/Linux"},{"type":"html","id":"ggm9MYpVak4_text","label":"ggm9MYpVak4_text","default":"Output File Format"},{"type":"html","id":"ggQq3Gxjx1X_text","label":"ggQq3Gxjx1X_text","default":".gcode"},{"type":"html","id":"gg6T5TH0uxu_text","label":"gg6T5TH0uxu_text","default":"input Formats"},{"type":"html","id":"ggXspPGtY7t_text","label":"ggXspPGtY7t_text","default":".stl/.obj/.3mf/.step/ .stp/.iges/.igs/.oltp/ .jpg/.jpeg/.png/.bmp"},{"type":"html","id":"ggDUjP3YK38_text","label":"ggDUjP3YK38_text","default":"Parameter"},{"type":"html","id":"gg_C3sKgWOz_text","label":"gg_C3sKgWOz_text","default":"Specification"},{"type":"html","id":"ggjnx9SxWbR_text","label":"ggjnx9SxWbR_text","default":"Parameter"},{"type":"html","id":"ggOjn_apI46_text","label":"ggOjn_apI46_text","default":"Specification"},{"type":"html","id":"ggcSdDcqLBD_text","label":"ggcSdDcqLBD_text","default":"Input Voltage"},{"type":"html","id":"gggaCxEmOx__text","label":"gggaCxEmOx__text","default":"110VAC/220VAC, 50/60Hz"},{"type":"html","id":"ggmGmAhA2uj_text","label":"ggmGmAhA2uj_text","default":"Memory&nbsp;"},{"type":"html","id":"ggZO_UQcR-H_text","label":"ggZO_UQcR-H_text","default":"16GB-SD, 1GB DDR3"},{"type":"html","id":"gg9fql8z26k_text","label":"gg9fql8z26k_text","default":"aximum Power&nbsp;"},{"type":"html","id":"ggB2TjAILZO_text","label":"ggB2TjAILZO_text","default":"800W"},{"type":"html","id":"ggjfUTSzmAu_text","label":"ggjfUTSzmAu_text","default":"User Interface&nbsp;"},{"type":"html","id":"ggOQc7ZFCgj_text","label":"ggOQc7ZFCgj_text","default":"4.3-inch touchscreen with 800×480 resolution"},{"type":"html","id":"ggfut8Kzq5b_text","label":"ggfut8Kzq5b_text","default":"Main Controller&nbsp;"},{"type":"html","id":"ggQQnVpm8g-_text","label":"ggQQnVpm8g-_text","default":"64-bit 1.5GHz Quad-core Cortex-A53 processor"},{"type":"html","id":"ggqQJMvUaBV_text","label":"ggqQJMvUaBV_text","default":"Printing Firmware Klipper"},{"type":"html","id":"ggp8CzR0o0k_text","label":"ggp8CzR0o0k_text","default":"Klipper"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
