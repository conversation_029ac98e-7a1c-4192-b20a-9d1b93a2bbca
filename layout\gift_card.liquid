<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.color_button }}">
  <link rel="canonical" href="{{ canonical_url }}">

  {%- if settings.favicon != blank -%}
    <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png" />
  {%- endif -%}

  {%- assign formatted_initial_value = gift_card.initial_value | money_without_trailing_zeros: gift_card.currency -%}
  {%- assign formatted_initial_value_stripped = formatted_initial_value | strip_html -%}
  <title>{{ 'gift_cards.issued.title_html' | t: value: formatted_initial_value_stripped, shop: shop.name }}</title>

  <meta name="description" content="{{ 'gift_cards.issued.subtext' | t }}">

  {%- render 'social-meta-tags' -%}

  {%- render 'font-face' -%}
  {{ 'theme.css' | asset_url | stylesheet_tag: preload: true }}
  {%- render 'css-variables' -%}

  <script>
    document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
  </script>

  <script src="{{ 'vendor-scripts-v14.js' | asset_url | split: '?' | first }}" defer="defer"></script>
  {{ 'vendor/qrcode.js' | shopify_asset_url | script_tag }}

  {{ content_for_header }}
</head>

<body class="template-giftcard" data-transitions="{{ settings.animate_page_transitions }}" data-type_header_capitalize="{{ settings.type_header_capitalize }}" data-type_base_accent_transform="{{ settings.type_base_accent_transform }}" data-type_header_accent_transform="{{ settings.type_header_accent_transform }}" data-animate_sections="{{ settings.animate_sections }}" data-animate_underlines="{{ settings.animate_underlines }}" data-animate_buttons="{{ settings.animate_buttons }}" data-animate_images="{{ settings.animate_images }}" data-animate_page_transition_style="{{ settings.animate_page_transition_style }}" data-type_header_text_alignment="{{ settings.type_headers_align_text }}" data-animate_images_style="{{ settings.animate_images_style | default: "zoom-fade" }}">

  <div id="PageContainer">

    <div class="page-width">

      {% section 'giftcard-header' %}

      <main class="giftcard" role="main">
        {{ content_for_layout }}
      </main>

      <footer class="giftcard__footer">
        {%- if gift_card.pass_url -%}
          <a class="add-to-apple-wallet" href="{{ gift_card.pass_url }}">
            {% assign alt_text = 'gift_cards.issued.add_to_apple_wallet' | t %}
            {%- render 'image-element',
              asset: 'gift-card/add-to-apple-wallet.svg',
              type: 'asset',
              img_width: 120,
              img_height: 40,
              alt: alt_text,
              sizes: '120px',
            -%}
          </a>
        {%- endif -%}
      </footer>
    </div>

  </div>

</body>
</html>
