document.addEventListener('DOMContentLoaded', function() {
  const pageNavigation = new PageNavigation();
  pageNavigation.init();

  // Apply mobile tooltip styles immediately
  if (window.innerWidth <= 768) {
    let mobileStyle = document.getElementById('mobile-tooltip-override');
    if (!mobileStyle) {
      mobileStyle = document.createElement('style');
      mobileStyle.id = 'mobile-tooltip-override';
      document.head.appendChild(mobileStyle);
    }

    mobileStyle.innerHTML = `
      @media (max-width: 768px) {
        #scroll-lock-tooltip {
          max-width: calc(100vw - 3rem) !important;
          font-size: 0.875rem !important;
          padding: 1.5rem !important;
          border-radius: 20px !important;
          backdrop-filter: blur(25px) !important;
          background: rgba(15, 15, 15, 0.98) !important;
          border: 1.5px solid rgba(0, 255, 136, 0.5) !important;
          box-shadow:
            0 25px 80px rgba(0, 0, 0, 0.8),
            0 10px 40px rgba(0, 0, 0, 0.6),
            0 4px 16px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(0, 255, 136, 0.2) !important;
          transform: translateY(20px) scale(0.85) !important;
          transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
        }

        #scroll-lock-tooltip.show {
          transform: translateY(0) scale(1) !important;
        }

        #scroll-lock-tooltip .tooltip-title {
          font-size: 1.1rem !important;
          font-weight: 600 !important;
          margin-bottom: 0.75rem !important;
        }

        #scroll-lock-tooltip .tooltip-description {
          font-size: 0.875rem !important;
          line-height: 1.6 !important;
        }

        #scroll-lock-tooltip .tooltip-arrow {
          width: 10px !important;
          height: 10px !important;
          background: rgba(15, 15, 15, 0.98) !important;
          border: 1.5px solid rgba(0, 255, 136, 0.5) !important;
        }

        #scroll-lock-tooltip .tooltip-content .tooltip-icon {
          font-size: 1.5rem !important;
          margin-right: 1rem !important;
        }
      }
    `;
  }

  // Make navigation available globally for debugging
  window.pageNavigation = pageNavigation;
  window.debugNavigation = function() {
    console.log('🔍 Navigation Debug Info:');
    console.log('- Sections found:', pageNavigation.sections.length);
    console.log('- Sections:', pageNavigation.sections.map(s => s.name));
    console.log('- Scroll lock active:', pageNavigation.isScrollLockActive());
    console.log('- Current locked component:', pageNavigation.currentLockedComponent);
    console.log('- Current section:', pageNavigation.currentSection?.name || 'none');
    console.log('- Body classes:', Array.from(document.body.classList));
    console.log('- Body style position:', document.body.style.position);
    console.log('- Pin spacers found:', document.querySelectorAll('.pin-spacer').length);
  };

  console.log('✅ Page Navigation initialized. Use debugNavigation() for debug info.');
});

class PageNavigation {
  constructor() {
    this.navList = document.getElementById('nav-list');
    this.progressBar = document.getElementById('progress-bar');
    this.sections = [];
    this.currentSection = null;
    this.observer = null;
    
    // 🔧 核心修复：添加状态管理
    this.isNavigating = false; // 导航跳转状态标志
    this.currentActiveSectionId = null; // 当前激活的组件ID
    this.navigationTimeout = null; // 导航完成超时器
    this.lastScrollTime = 0; // 最后滚动时间
    this.scrollLockState = false; // 滚动锁定状态缓存

    // Mobile navigation elements
    this.mobileNavTrigger = document.getElementById('mobile-nav-trigger');
    this.mobileNavContainer = document.getElementById('page-nav-container');
    this.mobileNavOverlay = document.getElementById('mobile-nav-overlay');
    this.mobileNavClose = document.getElementById('mobile-nav-close');
    this.triggerProgress = document.getElementById('trigger-progress');
    this.isMobileNavOpen = false;
  }

  init() {
    // Wait for DOM to be fully loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.initializeNavigation();
      });
    } else {
      this.initializeNavigation();
    }
  }

  initializeNavigation() {
    this.generateNavItems();
    this.setupScrollObserver();
    this.bindEvents();
    this.setupMobileNavigation();
    this.updateNavigationVisibility();
  }

  generateNavItems() {
    if (!this.navList) return;

    // Clear existing items
    this.navList.innerHTML = '';

    // Define the specific sections from page.json (excluding page-navigation itself)
    const pageSections = [
      { id: 'ir3_hero_section_1_M3ezat', name: 'Hero Section', hasScrollLock: false },
      { id: 'ir3_v2_key_features_N7xLp9', name: 'Key Features', hasScrollLock: true, lockType: 'interactive' },
      { id: 'ir3_v2_auto_leveling_frames_Nf8mK4', name: 'Smart Leveling', hasScrollLock: false, lockType: 'frames' },
      { id: 'ir3_tech_innovation_Kx9mN2', name: 'Tech Innovation', hasScrollLock: false },
      { id: 'ir3_video_scroll_demo_Kx8mN3', name: 'Klipper Firmware', hasScrollLock: false },
      { id: 'ir3_performance_video_scroll_Pf4mN5', name: 'Performance', hasScrollLock: false },
      { id: 'smart_features_Sm4rtF3', name: 'Smart Features', hasScrollLock: true, lockType: 'interactive', group: 'Smart Features' },
      // 移除独立的Filament Sensor导航项，现在归属于Smart Features
      { id: 'ir3_batch_printing_video_Bp8mN9', name: 'Batch Printing', hasScrollLock: false },
      { id: 'ir3_long_object_printing_Lg9mN4', name: 'Long Object Printing', hasScrollLock: false },
      { id: 'ir3_parameter_display_Pm8rD1', name: 'Technical Specifications', hasScrollLock: false }
    ];

    let validSectionIndex = 0;

    pageSections.forEach((section) => {
      // Try multiple selectors to find the section element
      let sectionElement =
        // Try exact shopify section ID
        document.querySelector(`#shopify-section-template--19961498599677__${section.id}`) ||
        document.querySelector(`#shopify-section-${section.id}`) ||
        // Try section with template prefix
        document.querySelector(`[id*="template--19961498599677__${section.id}"]`) ||
        // Try data attribute
        document.querySelector(`[data-section-id="${section.id}"]`) ||
        // Try partial ID match
        document.querySelector(`[id*="${section.id}"]`) ||
        // Try class match
        document.querySelector(`section[class*="${section.id}"]`);

      // If still not found, try to find by section type
      if (!sectionElement) {
        const sectionType = section.id.split('_')[0];
        sectionElement = document.querySelector(`[data-section-type*="${sectionType}"]`);
      }

      if (sectionElement) {
        this.sections.push({
          element: sectionElement,
          id: section.id,
          name: section.name,
          index: validSectionIndex,
          hasScrollLock: section.hasScrollLock,
          lockType: section.lockType
        });

        const navItem = this.createNavItem(section, validSectionIndex);
        this.navList.appendChild(navItem);
        validSectionIndex++;
      } else {
        console.warn(`Section not found: ${section.id}`);
      }
    });

    // If no sections found, try to auto-detect sections
    if (this.sections.length === 0) {
      this.autoDetectSections();
    }

    // Show navigation if sections found
    if (this.sections.length > 0) {
      this.updateNavigationVisibility();
    }
  }

  createNavItem(section, index) {
    const li = document.createElement('li');
    li.className = 'page-nav-item';

    const link = document.createElement('a');
    link.href = `#${section.id}`;
    link.className = 'page-nav-link';
    link.textContent = section.name;
    link.dataset.sectionId = section.id;
    link.dataset.index = index;
    link.dataset.hasScrollLock = section.hasScrollLock || false;
    link.dataset.lockType = section.lockType || '';

    // Add scroll lock class for identification (no visual indicator)
    if (section.hasScrollLock) {
      link.classList.add('has-scroll-lock');
    }

    // Add click handler for smooth scrolling
    link.addEventListener('click', (e) => {
      e.preventDefault();
      this.handleNavigationClick(section.id);

      // Close mobile nav if open
      if (this.isMobileNavOpen) {
        this.closeMobileNav();
      }
    });

    li.appendChild(link);
    return li;
  }

  // 🔧 核心修复：新的导航点击处理方法
  handleNavigationClick(sectionId) {
    console.log('🔍 Navigation clicked:', sectionId);

    // 检查滚动锁定状态
    const isLocked = this.isScrollLockActive();
    console.log('🔒 Scroll lock status:', isLocked);

    if (isLocked) {
      console.log('⚠️ Navigation blocked due to scroll lock');
      // 显示提示工具提示
      const navLink = document.querySelector(`[data-section-id="${sectionId}"]`);
      if (navLink) {
        this.showScrollLockBlockTooltip(navLink);
      }
      return; // 不执行导航
    }

    // 🔧 设置导航状态，防止滚动观察器干扰
    this.isNavigating = true;
    
    // 🔧 立即更新激活状态，避免竞态条件
    this.forceSetActiveSection(sectionId);
    
    // 执行滚动导航
    this.scrollToSection(sectionId);
    
    // 🔧 设置超时器，导航完成后重置状态
    clearTimeout(this.navigationTimeout);
    this.navigationTimeout = setTimeout(() => {
      this.isNavigating = false;
      console.log('✅ Navigation completed, scroll observer re-enabled');
    }, 1000); // 1秒后重新启用滚动观察器
  }

  // 🔧 核心修复：强制设置激活组件，确保原子性操作
  forceSetActiveSection(sectionId) {
    console.log('🎯 Force setting active section:', sectionId);
    
    // 使用 requestAnimationFrame 确保DOM操作的原子性
    requestAnimationFrame(() => {
      // 1. 首先移除所有激活状态，确保清理彻底
      const allLinks = document.querySelectorAll('.page-nav-link');
      allLinks.forEach(link => {
        if (link.classList.contains('active')) {
          link.classList.remove('active');
          console.log('🧹 Removed active from:', link.dataset.sectionId);
        }
      });
      
      // 2. 然后添加新的激活状态
      const targetLink = document.querySelector(`[data-section-id="${sectionId}"]`);
      if (targetLink) {
        targetLink.classList.add('active');
        console.log('✅ Added active to:', sectionId);
        
        // 更新当前激活组件ID
        this.currentActiveSectionId = sectionId;
        
        // 更新当前组件数据
        const sectionData = this.sections.find(s => s.id === sectionId);
        if (sectionData) {
          this.currentSection = sectionData;
        }
      } else {
        console.error('❌ Target link not found for section:', sectionId);
      }
    });
  }

  autoDetectSections() {
    // Auto-detect sections based on common Shopify section patterns
    const sectionElements = document.querySelectorAll('[id^="shopify-section-"], section[class*="section"], .section');

    sectionElements.forEach((element, index) => {
      if (element.id && element.id.includes('shopify-section-')) {
        const sectionId = element.id.replace('shopify-section-', '');
        const sectionName = this.generateSectionName(sectionId);

        // Skip navigation section itself
        if (sectionId.includes('page_navigation') || sectionId.includes('page-navigation')) {
          return;
        }

        this.sections.push({
          element: element,
          id: sectionId,
          name: sectionName,
          index: index
        });

        const navItem = this.createNavItem({ id: sectionId, name: sectionName }, index);
        this.navList.appendChild(navItem);
      }
    });
  }

  generateSectionName(sectionId) {
    // Convert section ID to readable name
    return sectionId
      .replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .replace(/\s+/g, ' ')
      .trim();
  }

  setupScrollObserver() {
    const options = {
      root: null,
      rootMargin: '-20% 0px -20% 0px',
      threshold: [0, 0.1, 0.5, 1]
    };

    this.observer = new IntersectionObserver((entries) => {
      // 🔧 核心修复：导航期间忽略滚动观察器
      if (this.isNavigating) {
        console.log('⏸️ Navigation in progress, ignoring scroll observer');
        return;
      }

      // 🔧 防抖处理，避免频繁触发
      const now = Date.now();
      if (now - this.lastScrollTime < 100) {
        return; // 100ms内的重复事件忽略
      }
      this.lastScrollTime = now;

      entries.forEach(entry => {
        if (entry.isIntersecting && entry.intersectionRatio > 0.1) {
          // Find the matching section in our sections array
          const matchingSection = this.sections.find(s => s.element === entry.target);
          if (matchingSection) {
            // 🔧 只有当不在导航状态时才更新激活状态
            console.log('👁️ Scroll observer detected section:', matchingSection.id);
            this.setActiveSection(matchingSection.id);
            this.updateProgress();
          }
        }
      });
    }, options);

    // Observe all sections
    this.sections.forEach(section => {
      this.observer.observe(section.element);
    });
  }

  // 🔧 核心修复：改进的设置激活组件方法
  setActiveSection(sectionId) {
    // 🔧 如果正在导航中，忽略滚动观察器的调用
    if (this.isNavigating) {
      console.log('⏸️ Ignoring setActiveSection during navigation');
      return;
    }

    // 🔧 如果当前激活的组件已经是目标组件，避免重复操作
    if (this.currentActiveSectionId === sectionId) {
      console.log('⏭️ Section already active, skipping:', sectionId);
      return;
    }

    console.log('🎯 Setting active section via scroll:', sectionId);

    // 使用防抖机制，避免快速连续调用
    clearTimeout(this.setActiveSectionTimeout);
    this.setActiveSectionTimeout = setTimeout(() => {
      // 确保原子性操作
      requestAnimationFrame(() => {
        // Remove active class from all links
        document.querySelectorAll('.page-nav-link').forEach(link => {
          link.classList.remove('active');
        });

        // Try to find the active link by section ID
        let activeLink = document.querySelector(`[data-section-id="${sectionId}"]`);

        // If not found, try to match by partial ID (for Shopify sections)
        if (!activeLink) {
          // Extract the base section ID from Shopify section format
          const baseSectionId = sectionId.replace(/^shopify-section-.*__/, '').replace(/^template--.*__/, '');
          activeLink = document.querySelector(`[data-section-id="${baseSectionId}"]`);
        }

        // If still not found, try to find by element ID
        if (!activeLink) {
          const sectionElement = document.getElementById(sectionId);
          if (sectionElement) {
            // Find the corresponding section in our sections array
            const matchingSection = this.sections.find(s => s.element === sectionElement);
            if (matchingSection) {
              activeLink = document.querySelector(`[data-section-id="${matchingSection.id}"]`);
            }
          }
        }

        if (activeLink) {
          activeLink.classList.add('active');
          
          // 🔧 更新当前激活组件ID
          this.currentActiveSectionId = sectionId;
          
          const sectionData = this.sections.find(s => s.id === activeLink.dataset.sectionId);
          if (sectionData) {
            this.currentSection = sectionData;
          }
          
          console.log('✅ Active section set via scroll:', sectionId);
        }
      });
    }, 50); // 50ms防抖
  }

  updateProgress() {
    if (!this.progressBar) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = Math.min(Math.max(scrollTop / scrollHeight, 0), 1);

    this.progressBar.style.setProperty('--progress', `${progress * 100}%`);
    
    // Update mobile trigger progress
    if (this.triggerProgress) {
      this.triggerProgress.style.setProperty('--progress', `${progress * 100}%`);
    }
  }

  bindEvents() {
    // Handle scroll for progress updates
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.updateProgress();
      }, 10);
    }, { passive: true });

    // Handle navigation clicks with scroll lock detection
    document.addEventListener('click', (e) => {
      const navLink = e.target.closest('.page-nav-link');
      if (navLink) {
        e.preventDefault();
        const sectionId = navLink.dataset.sectionId;

        if (sectionId) {
          // 🔧 使用新的导航点击处理方法
          this.handleNavigationClick(sectionId);
        }
      }
    });

    // Handle window resize
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Close mobile nav if switching to desktop
        if (window.innerWidth > 768 && this.isMobileNavOpen) {
          this.closeMobileNav();
        }
        this.updateNavigationVisibility();
      }, 250);
    });

    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // Refresh observer when tab becomes visible
        this.refreshObserver();
      }
    });
  }

  refreshObserver() {
    if (this.observer) {
      this.observer.disconnect();
      this.setupScrollObserver();
    }
  }

  scrollToSection(sectionId) {
    console.log('🎯 scrollToSection called with:', sectionId);
    const section = this.sections.find(s => s.id === sectionId);
    if (!section) {
      console.error('❌ Section not found:', sectionId);
      return;
    }

    console.log('✅ Section found:', section.name);
    const targetElement = section.element;

    // Calculate header offset dynamically
    const header = document.querySelector('header, .header, [class*="header"]');
    const headerHeight = header ? header.offsetHeight : 0;
    const additionalOffset = 20; // Extra spacing
    const totalOffset = headerHeight + additionalOffset;

    // Get target position
    const elementPosition = targetElement.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - totalOffset;

    // 触发导航跳转事件，通知其他组件
    document.dispatchEvent(new CustomEvent('pageNavigationJump', {
      detail: { sectionId: sectionId, targetElement: targetElement }
    }));

    // Add visual feedback
    this.addScrollingFeedback(sectionId);

    // Smooth scroll with custom easing
    console.log('🚀 Starting smooth scroll to position:', offsetPosition);
    this.smoothScrollTo(offsetPosition, 800);
  }

  addScrollingFeedback(sectionId) {
    // Add temporary visual feedback to the clicked nav item
    const navLink = document.querySelector(`[data-section-id="${sectionId}"]`);
    if (navLink) {
      navLink.style.transform = 'scale(0.95)';
      navLink.style.transition = 'transform 0.1s ease';

      setTimeout(() => {
        navLink.style.transform = '';
        navLink.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
      }, 100);
    }
  }

  smoothScrollTo(targetPosition, duration = 800) {
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    // Easing function for smooth animation
    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };

    const animation = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);

      const easedProgress = easeInOutCubic(progress);
      const currentPosition = startPosition + (distance * easedProgress);

      window.scrollTo(0, currentPosition);

      if (progress < 1) {
        requestAnimationFrame(animation);
      } else {
        console.log('✅ Smooth scroll completed');
      }
    };

    requestAnimationFrame(animation);
  }

  isScrollLockActive() {
    // 🔧 使用缓存机制，避免频繁DOM查询
    const now = Date.now();
    if (this.scrollLockCacheTime && (now - this.scrollLockCacheTime < 100)) {
      return this.scrollLockState;
    }

    let lockReasons = [];
    let lockedComponent = null;

    // Method 1: Check body scroll-locked class (ir3-v2-key-features and smart-features)
    if (document.body.classList.contains('scroll-locked')) {
      lockReasons.push('body.scroll-locked');

      // Try to identify which component is locked
      const keyFeaturesSection = document.querySelector('[data-section-type="ir3-v2-key-features"]');
      const smartFeaturesSection = document.querySelector('[data-section-type="smart-features"]');

      if (keyFeaturesSection) {
        const rect = keyFeaturesSection.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= window.innerHeight * 0.3) {
          lockedComponent = { name: 'Key Features', type: 'interactive', element: keyFeaturesSection };
        }
      }

      if (smartFeaturesSection && !lockedComponent) {
        const rect = smartFeaturesSection.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= window.innerHeight * 0.3) {
          lockedComponent = { name: 'Smart Features', type: 'interactive', element: smartFeaturesSection };
        }
      }
    }

    // Method 2: Check body fixed positioning
    if (document.body.style.position === 'fixed' && document.body.style.overflow === 'hidden') {
      lockReasons.push('body fixed positioning');
    }

    const isLocked = lockReasons.length > 0;

    // 🔧 缓存结果
    this.scrollLockState = isLocked;
    this.scrollLockCacheTime = now;
    this.currentLockedComponent = lockedComponent;

    if (isLocked) {
      console.log('🔒 Scroll lock detected:', lockReasons.join(', '));
      if (lockedComponent) {
        console.log('📍 Locked component:', lockedComponent.name, `(${lockedComponent.type})`);
      }
    }

    return isLocked;
  }

  getCurrentLockedSection() {
    // Return the locked component identified in isScrollLockActive()
    if (this.currentLockedComponent) {
      return this.currentLockedComponent;
    }

    // Fallback: try to identify based on viewport position
    const lockedSections = [
      { id: 'ir3_v2_key_features_N7xLp9', name: 'Key Features', type: 'interactive' },
      { id: 'ir3_v2_auto_leveling_frames_Nf8mK4', name: 'Smart Leveling', type: 'frames' },
      { id: 'smart_features_Sm4rtF3', name: 'Smart Features', type: 'interactive' },
      { id: 'ir3_filament_sensor_Fs8nR2', name: 'Filament Sensor', type: 'interactive' }
    ];

    for (const sectionInfo of lockedSections) {
      const section = this.sections.find(s => s.id === sectionInfo.id);
      if (section) {
        const rect = section.element.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        // Check if section is currently in viewport and likely locked
        if (rect.top <= 100 && rect.bottom >= viewportHeight * 0.5) {
          return {
            name: sectionInfo.name,
            type: sectionInfo.type,
            element: section.element
          };
        }
      }
    }

    return null;
  }

  showScrollLockBlockTooltip(targetElement) {
    const tooltip = document.getElementById('scroll-lock-tooltip');
    const tooltipIcon = document.getElementById('tooltip-icon');
    const tooltipTitle = document.getElementById('tooltip-title');
    const tooltipDescription = document.getElementById('tooltip-description');

    if (!tooltip) return;

    const currentLockedSection = this.getCurrentLockedSection();
    const targetSectionName = targetElement.textContent.replace(/[🎬🔒]/g, '').trim();

    // Update tooltip content for scroll lock blocking
    tooltipIcon.textContent = '⚠️';
    tooltipTitle.textContent = 'Navigation Blocked';

    if (currentLockedSection) {
      const lockedSectionName = currentLockedSection.name;
      const lockType = currentLockedSection.type;

      if (lockType === 'video') {
        tooltipDescription.textContent = `"${lockedSectionName}" is currently active. Please use your mouse wheel to scroll through the entire video content, then you can navigate to "${targetSectionName}".`;
      } else if (lockType === 'interactive') {
        tooltipDescription.textContent = `"${lockedSectionName}" is currently active. Please use your mouse wheel to browse through all interactive features, then you can navigate to "${targetSectionName}".`;
      }
    } else {
      tooltipDescription.textContent = `An interactive section is currently active. Please use your mouse wheel to scroll through all content, then you can navigate to "${targetSectionName}".`;
    }

    // Position tooltip
    this.positionTooltip(tooltip, targetElement);

    // Show tooltip
    tooltip.classList.add('show');

    // Auto hide after delay
    setTimeout(() => {
      this.hideScrollLockTooltip();
    }, 1500); // Quick tooltip display for better UX
  }

  hideScrollLockTooltip() {
    const tooltip = document.getElementById('scroll-lock-tooltip');
    if (tooltip) {
      tooltip.classList.remove('show');
    }
  }

  positionTooltip(tooltip, targetElement) {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const isMobile = viewportWidth <= 768;

    // Declare variables
    let left, top, arrowClass;

    if (isMobile) {
      // On mobile, use CSS transform for perfect centering
      left = viewportWidth / 2;
      top = viewportHeight / 2;

      // Set arrow class for mobile
      arrowClass = 'bottom';
    } else {
      // Desktop positioning (original logic)
      const targetRect = targetElement.getBoundingClientRect();
      const tooltipRect = tooltip.getBoundingClientRect();
      const tooltipWidth = tooltipRect.width;
      const tooltipHeight = tooltipRect.height;

      // Default position: right of target
      left = targetRect.right + 10;
      top = targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2);
      arrowClass = 'left';

      // Check if tooltip would go off screen on the right
      if (left + tooltipWidth > viewportWidth - 20) {
        // Position on left instead
        left = targetRect.left - tooltipWidth - 10;
        arrowClass = 'right';
      }

      // Check if tooltip would go off screen on the left
      if (left < 20) {
        // Position below target
        left = Math.max(20, targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2));
        top = targetRect.bottom + 10;
        arrowClass = 'top';
      }

      // Check if tooltip would go off screen at the bottom
      if (top + tooltipHeight > viewportHeight - 20) {
        // Position above target
        top = targetRect.top - tooltipHeight - 10;
        arrowClass = 'bottom';
      }

      // Final bounds check
      left = Math.max(20, Math.min(left, viewportWidth - tooltipWidth - 20));
      top = Math.max(20, Math.min(top, viewportHeight - tooltipHeight - 20));
    }

    // Apply position with !important to override CSS
    tooltip.style.setProperty('left', `${left}px`, 'important');
    tooltip.style.setProperty('top', `${top}px`, 'important');

    // For mobile, ensure the tooltip is properly positioned and scaled
    if (isMobile) {
      console.log('🔧 Mobile tooltip positioning:', {
        left,
        top,
        viewportWidth,
        viewportHeight,
        centerX: viewportWidth / 2,
        centerY: viewportHeight / 2
      });

      // Force position with !important and use transform for perfect centering
      tooltip.style.setProperty('position', 'fixed', 'important');
      tooltip.style.setProperty('left', `${left}px`, 'important');
      tooltip.style.setProperty('top', `${top}px`, 'important');
      tooltip.style.setProperty('transform', 'translate(-50%, -50%) translateY(20px) scale(0.85)', 'important');

      // When showing, apply the correct transform
      setTimeout(() => {
        if (tooltip.classList.contains('show')) {
          tooltip.style.setProperty('transform', 'translate(-50%, -50%) scale(1)', 'important');
        }
      }, 10);
    }

    // Update arrow position
    const arrow = tooltip.querySelector('.tooltip-arrow');
    if (arrow) {
      arrow.className = `tooltip-arrow ${arrowClass}`;
    }
  }

  setupMobileNavigation() {
    // Mobile navigation event handlers
    if (this.mobileNavTrigger) {
      this.mobileNavTrigger.addEventListener('click', () => {
        this.openMobileNav();
      });
    }

    if (this.mobileNavClose) {
      this.mobileNavClose.addEventListener('click', () => {
        this.closeMobileNav();
      });
    }

    if (this.mobileNavOverlay) {
      this.mobileNavOverlay.addEventListener('click', () => {
        this.closeMobileNav();
      });
    }

    // Handle escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isMobileNavOpen) {
        this.closeMobileNav();
      }
    });
  }

  openMobileNav() {
    if (this.mobileNavContainer) {
      this.mobileNavContainer.classList.add('active');
    }
    if (this.mobileNavOverlay) {
      this.mobileNavOverlay.classList.add('active');
    }
    this.isMobileNavOpen = true;

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  closeMobileNav() {
    if (this.mobileNavContainer) {
      this.mobileNavContainer.classList.remove('active');
    }
    if (this.mobileNavOverlay) {
      this.mobileNavOverlay.classList.remove('active');
    }
    this.isMobileNavOpen = false;

    // Restore body scroll
    document.body.style.overflow = '';
  }

  updateNavigationVisibility() {
    const navigation = document.querySelector('.page-navigation');
    if (!navigation) return;

    // Show navigation if sections are found
    if (this.sections.length > 0) {
      navigation.style.opacity = '1';
      navigation.style.pointerEvents = 'all';
    } else {
      navigation.style.opacity = '0';
      navigation.style.pointerEvents = 'none';
    }
  }
}