# PowerShell script to fix GemPages CSS preload issues
# This script removes the conditional CSS disabling logic from all GemPages section files

$sectionsPath = "sections"
$gempagesFiles = Get-ChildItem -Path $sectionsPath -Name "gp-section-*.liquid"

Write-Host "Found $($gempagesFiles.Count) GemPages section files to fix..."

foreach ($file in $gempagesFiles) {
    $filePath = Join-Path $sectionsPath $file
    Write-Host "Processing: $file"
    
    try {
        # Read the file content
        $content = Get-Content -Path $filePath -Raw
        
        # Replace the conditional CSS style tag with a simple style tag
        $oldPattern = '<style {% if section\.settings\.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>'
        $newPattern = '<style>'
        
        if ($content -match [regex]::Escape($oldPattern)) {
            $newContent = $content -replace [regex]::Escape($oldPattern), $newPattern
            
            # Write the updated content back to the file
            Set-Content -Path $filePath -Value $newContent -NoNewline
            Write-Host "  ✓ Fixed CSS preload in $file"
        } else {
            Write-Host "  - No CSS preload pattern found in $file"
        }
    }
    catch {
        Write-Host "  ✗ Error processing $file`: $($_.Exception.Message)"
    }
}

Write-Host "`nCompleted fixing GemPages CSS preload issues!"
Write-Host "Please restart Shopify CLI to apply the changes."
