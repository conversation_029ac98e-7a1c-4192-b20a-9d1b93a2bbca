# 🔐 Shopify CLI 认证指南

## 概述

新版Shopify CLI (v3.82.0+) 已经简化了认证流程。不再需要单独的`shopify auth login`命令，认证现在通过主题命令自动处理。

## 🚀 快速开始

### 首次认证

当您首次运行任何需要认证的Shopify CLI命令时，系统会自动启动认证流程：

```bash
# 启动开发服务器（会自动触发认证）
shopify theme dev

# 或指定特定商店
shopify theme dev --store=your-store.myshopify.com
```

### 认证流程

1. **显示验证码**: CLI会显示一个用户验证码（如：NBKX-LZFV）
2. **自动打开浏览器**: 系统会尝试自动打开默认浏览器
3. **手动认证**: 如果浏览器未自动打开，您可以：
   - 按任意键手动打开登录页面
   - 或访问显示的认证链接

## 🔧 认证方法

### 1. 标准认证（推荐）

```bash
# 直接运行主题命令，系统会自动处理认证
shopify theme dev
```

**优点**:
- 自动化程度高
- 安全性好
- 支持多商店管理

### 2. 指定商店认证

```bash
# 为特定商店进行认证
shopify theme dev --store=your-store.myshopify.com
```

**适用场景**:
- 管理多个Shopify商店
- 需要明确指定目标商店

### 3. Theme Access 密码认证

对于某些特殊情况，您可以使用Theme Access应用生成的密码：

```bash
# 使用Theme Access密码
shopify theme dev --password=your-theme-access-password
```

**获取Theme Access密码**:
1. 在Shopify Admin中安装"Theme Access"应用
2. 生成访问密码
3. 在CLI命令中使用该密码

## 🛠️ 常见认证场景

### 开发环境认证

```bash
# 启动开发服务器
shopify theme dev

# 拉取主题文件
shopify theme pull

# 推送主题文件
shopify theme push --unpublished
```

### 生产环境认证

```bash
# 推送到生产环境（需要额外确认）
shopify theme push --live --store=your-store.myshopify.com
```

### CI/CD 环境认证

对于自动化部署，建议使用Theme Access密码：

```bash
# 在CI/CD中使用环境变量
shopify theme push --password=$SHOPIFY_THEME_PASSWORD --store=$SHOPIFY_STORE_URL
```

## 🔍 故障排除

### 认证失败

**问题**: 认证过程中出现错误

**解决方案**:
```bash
# 1. 清除认证缓存
shopify auth logout

# 2. 重新运行命令
shopify theme dev
```

### 浏览器未自动打开

**问题**: 认证时浏览器没有自动打开

**解决方案**:
1. 按任意键手动触发浏览器打开
2. 复制显示的认证链接到浏览器
3. 手动访问 Shopify Partners 或 Admin 页面登录

### 多商店管理

**问题**: 需要在多个商店之间切换

**解决方案**:
```bash
# 方法1: 每次指定商店
shopify theme dev --store=store1.myshopify.com
shopify theme dev --store=store2.myshopify.com

# 方法2: 使用不同的项目目录
cd project-store1/
shopify theme dev

cd project-store2/
shopify theme dev
```

### 权限问题

**问题**: 提示权限不足或"You are not authorized to use the CLI"

**常见错误信息**:
```
You are not authorized to use the CLI to develop in the provided store: your-store.myshopify.com

You can't use Shopify CLI with development stores if you only have Partner staff member access.
```

**解决方案**:

#### 1. 开发商店权限问题
如果您使用的是开发商店（Development Store）：

**方法A: 成为商店所有者**
1. 在 Shopify Partners 后台找到您的开发商店
2. 点击"Transfer ownership"将商店转移给自己
3. 或者创建一个新的开发商店并设置自己为所有者

**方法B: 创建员工账户**
1. 登录到商店的 Shopify Admin
2. 进入 Settings → Users and permissions
3. 添加自己为员工，并给予"Themes"权限

**方法C: 直接登录商店**
1. 首先直接访问商店的 Admin URL: `https://your-store.myshopify.com/admin`
2. 使用商店所有者账户登录一次
3. 这会将开发商店与您的 Shopify 登录关联
4. 然后再使用 Shopify CLI

#### 2. 生产商店权限问题
如果您使用的是生产商店：
1. 确保您是商店所有者或有"Themes"权限的员工
2. 联系商店所有者添加必要权限
3. 检查是否为商店的合作者

## 🔒 安全最佳实践

### 1. 定期更新认证

```bash
# 定期登出并重新认证
shopify auth logout
shopify theme dev  # 重新认证
```

### 2. 使用环境变量

```bash
# 在 .env 文件中设置（不要提交到版本控制）
SHOPIFY_STORE_URL=your-store.myshopify.com
SHOPIFY_THEME_PASSWORD=your-theme-access-password
```

### 3. 限制访问权限

- 只给必要的团队成员提供主题开发权限
- 使用Theme Access应用生成临时密码
- 定期轮换访问密码

## 📚 相关命令

### 认证相关

```bash
# 登出（清除认证信息）
shopify auth logout

# 查看当前认证状态
shopify theme info
```

### 主题管理

```bash
# 列出所有主题
shopify theme list

# 查看主题信息
shopify theme info --theme=theme-name

# 打开主题预览
shopify theme open --theme=theme-name
```

## 🆘 获取帮助

如果遇到认证问题：

1. **查看帮助文档**:
   ```bash
   shopify theme --help
   shopify theme dev --help
   ```

2. **检查CLI版本**:
   ```bash
   shopify version
   ```

3. **联系支持**:
   - [Shopify Community](https://community.shopify.com/)
   - [Shopify Partners](https://partners.shopify.com/)
   - [GitHub Issues](https://github.com/shopify/cli/issues)

---

*最后更新: 2024年12月 - 基于Shopify CLI v3.82.0*
