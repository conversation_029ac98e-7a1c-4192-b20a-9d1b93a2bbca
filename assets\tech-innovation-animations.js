// Tech Innovation 组件动画优化
// 立即执行测试
console.log('🚀 tech-innovation-animations.js 文件已加载');

document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 DOMContentLoaded 事件触发');
  // 确保GSAP已加载
  if (typeof gsap === 'undefined') {
    console.warn('GSAP not loaded');
    return;
  }

  gsap.registerPlugin(ScrollTrigger);

  // 等待页面完全加载后再初始化ScrollTrigger
  window.addEventListener('load', function() {
    // 刷新ScrollTrigger以确保正确计算位置
    ScrollTrigger.refresh();
  });

  // 标题区域动画
  gsap.from('.section-header > *', {
    y: 30,
    opacity: 0,
    duration: 0.8,
    stagger: 0.2,
    ease: "power2.out",
    scrollTrigger: {
      trigger: '.section-header',
      start: 'top 80%',
      toggleActions: 'play none none reverse'
    }
  });

  // 技术对比区域动画
  gsap.from('.tech-side', {
    y: 50,
    opacity: 0,
    duration: 1,
    stagger: 0.3,
    ease: "power2.out",
    scrollTrigger: {
      trigger: '.comparison-container',
      start: 'top 70%',
      toggleActions: 'play none none reverse'
    }
  });

  // VS圆圈动画
  gsap.from('.vs-circle', {
    scale: 0,
    opacity: 0,
    duration: 0.6,
    ease: "back.out(1.7)",
    scrollTrigger: {
      trigger: '.vs-divider',
      start: 'top 80%',
      toggleActions: 'play none none reverse'
    }
  });

  // Tech-data 优化动画管理 - Apple风格流畅动画
  gsap.from('.tech-info > *', {
    y: 30,
    opacity: 0,
    duration: 0.8,
    stagger: 0.2,
    ease: "power2.out",
    scrollTrigger: {
      trigger: '.tech-info',
      start: 'top 89%',
      toggleActions: 'play none none reverse'
    }
  });

  // Tech-data 优化动画管理 - Apple风格流畅动画
  // 预设初始状态以避免闪烁
  gsap.set('.data-item', {
    y: 40,
    opacity: 0,
    scale: 0.95,
    transformOrigin: 'center bottom'
  });

  gsap.set('.data-value', {
    y: 15,
    opacity: 0,
    scale: 0.8
  });

  gsap.set('.data-label', {
    y: 10,
    opacity: 0
  });

  // 验证元素存在性并创建独立的 ScrollTrigger 用于回调事件监听
  const techDataElement = document.querySelector('.tech-data');
  console.log('🔍 Tech-data 元素检查:', {
    element: techDataElement,
    exists: !!techDataElement,
    rect: techDataElement ? techDataElement.getBoundingClientRect() : null
  });

  if (techDataElement) {
    // 创建独立的 ScrollTrigger 专门用于回调事件
    ScrollTrigger.create({
      trigger: techDataElement,
      start: 'top 90%',  // 稍微调整触发点
      end: 'bottom 10%', // 扩大结束区域
      markers: false,    // 可以临时设为 true 来调试
      onEnter: function(self) {
        console.log('⚡ 进入 Tech-data 触发区域 - 独立触发器', {
          progress: self.progress,
          direction: self.direction
        });
      },
      onLeave: function(self) {
        console.log('👋 离开 Tech-data 触发区域 - 独立触发器修复版', {
          progress: self.progress,
          direction: self.direction
        });
      },
      onEnterBack: function(self) {
        console.log('🔄 重新进入 Tech-data 触发区域 - 独立触发器', {
          progress: self.progress,
          direction: self.direction
        });
      },
      onLeaveBack: function(self) {
        console.log('⬆️ 向上离开 Tech-data 触发区域 - 独立触发器', {
          progress: self.progress,
          direction: self.direction
        });
      },
      onToggle: function(self) {
        console.log('� Tech-data 触发状态切换:', {
          isActive: self.isActive,
          progress: self.progress,
          direction: self.direction
        });
      }
    });
  } else {
    console.error('❌ .tech-data 元素未找到，无法创建 ScrollTrigger 回调');
  }

  // 创建主动画时间线 - 优化的Apple风格动画（简化版，专注动画）
  let techDataTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: '.tech-data',  // 直接使用tech-data区域作为触发点
      start: 'top 88%',  // 当tech-data区域顶部进入视口88%位置时触发
      end: 'bottom 20%',  // 扩大触发区域，当tech-data区域底部离开视口20%位置时结束
      toggleActions: 'play pause resume reverse',  // 修复toggleActions以支持所有回调
      once: false,  // 允许重复触发
      markers: false,
      anticipatePin: 1,  // 预测滚动，提升性能
      refreshPriority: -1,  // 降低刷新优先级
      fastScrollEnd: true,  // 快速滚动时立即完成动画
      invalidateOnRefresh: true,  // 刷新时重新计算
      preventOverlaps: true,  // 防止动画重叠
      scrub: false,  // 确保不与滚动绑定，保持独立动画
      onStart: function() {
        console.log('🚀 Tech-data 卡片动画开始 - 优化版');
        // 确保动画开始时元素可见
        gsap.set('.tech-data', { visibility: 'visible' });
      },
      onComplete: function() {
        console.log('✅ Tech-data 卡片动画完成 - 流畅过渡');
      },
      onRefresh: function() {
        console.log('🔄 ScrollTrigger 刷新 - tech-data');
      }
    }
  });

  // 优化的动画序列 - 更短的持续时间和更流畅的缓动
  techDataTimeline
    // 第一阶段：卡片容器动画 - 快速且流畅
    .to('.data-item', {
      y: 0,
      opacity: 1,
      scale: 1,
      duration: 0.6,  // 缩短持续时间
      stagger: {
        amount: 0.3,  // 总stagger时间缩短
        from: "start",
        ease: "power2.out"
      },
      ease: "power3.out",  // 更流畅的缓动函数
      force3D: true  // 强制GPU加速
    }, 0)

    // 第二阶段：数值动画 - 与容器动画重叠开始
    .to('.data-value', {
      y: 0,
      opacity: 1,
      scale: 1,
      duration: 0.7,
      stagger: {
        amount: 0.25,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.2)",  // 减少back效果强度
      force3D: true
    }, 0.15)  // 稍微延迟开始，创造层次感

    // 第三阶段：标签动画 - 最后出现
    .to('.data-label', {
      y: 0,
      opacity: 1,
      duration: 0.5,
      stagger: {
        amount: 0.2,
        from: "start",
        ease: "power2.out"
      },
      ease: "power2.out",
      force3D: true
    }, 0.25);  // 与数值动画重叠

  // 高级性能优化：预加载和缓存
  gsap.set('.data-item, .data-value, .data-label', {
    willChange: 'transform, opacity'
  });

  // 添加动画完成后的清理函数，释放资源
  techDataTimeline.eventCallback("onComplete", function() {
    // 动画完成后移除willChange属性，减少内存占用
    gsap.set('.data-item, .data-value, .data-label', {
      willChange: 'auto',
      clearProps: 'willChange'
    });
  });

  // 创建性能监控函数
  const monitorPerformance = () => {
    // 检测帧率下降
    let lastTime = 0;
    let frameTimes = [];
    let frameDrops = 0;

    const checkFrameRate = (time) => {
      if (lastTime) {
        const delta = time - lastTime;
        frameTimes.push(delta);

        // 保持最近30帧的记录
        if (frameTimes.length > 30) {
          frameTimes.shift();
        }

        // 检测帧率下降
        if (delta > 32) { // 低于30fps
          frameDrops++;

          // 如果连续出现帧率下降，简化动画
          if (frameDrops > 3) {
            simplifyAnimations();
            frameDrops = 0;
          }
        }
      }
      lastTime = time;
      requestAnimationFrame(checkFrameRate);
    };

    // 启动性能监控
    requestAnimationFrame(checkFrameRate);
  };

  // 简化动画函数 - 在性能不佳时调用
  const simplifyAnimations = () => {
    // 清除当前动画
    techDataTimeline.clear();

    // 应用更简单的动画
    techDataTimeline
      .to('.data-item', {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.3,
        stagger: 0.05,
        ease: "power1.out",
        force3D: true
      }, 0)
      .to('.data-value, .data-label', {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.3,
        stagger: 0.05,
        ease: "power1.out",
        force3D: true
      }, 0.1);
  };

  // 移动端性能优化
  if (window.innerWidth <= 768) {
    // 移动端使用更简化的动画
    techDataTimeline.clear();

    techDataTimeline
      .to('.data-item', {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.4,
        stagger: 0.06,  // 更短的stagger
        ease: "power2.out",
        force3D: true
      }, 0)
      .to('.data-value, .data-label', {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.3,
        stagger: 0.05,
        ease: "power2.out",
        force3D: true
      }, 0.1);
  }

  // 启动性能监控
  if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    monitorPerformance();
  }

  // 交互式视频控制功能
  const initInteractiveVideo = () => {
    console.log('🚀 initInteractiveVideo 开始执行');

    const interactiveContainer = document.querySelector('.interactive-media-container');
    const video = document.querySelector('.interactive-video');
    const image = document.querySelector('.static-image');

    console.log('🔍 元素查找结果:', {
      container: !!interactiveContainer,
      video: !!video,
      image: !!image
    });

    if (!interactiveContainer || !video || !image) {
      console.warn('❌ Interactive video elements not found:', {
        container: !!interactiveContainer,
        video: !!video,
        image: !!image
      });
      return;
    }

    // 预加载视频
    video.load();

    // 设置初始状态：图片显示，视频隐藏
    video.style.opacity = '0';
    image.style.opacity = '1';

    // 定义事件处理函数以便移除
    const handleMouseEnter = () => {
      // 检查VS模式是否激活
      if (window.vsMediaController && window.vsMediaController.isPlaying) {
        console.log('🚫 鼠标进入：VS模式播放中，忽略悬停 (tech-animations.js)');
        return;
      }

      console.log('🎯 鼠标进入：开始淡化到视频');

      // 直接设置样式，触发CSS过渡效果
      video.style.opacity = '1';
      image.style.opacity = '0';

      // 播放视频
      video.currentTime = 0;
      video.play().catch(error => {
        console.warn('Video play failed:', error);
      });
    };

    const handleMouseLeave = () => {
      // 检查VS模式是否激活
      if (window.vsMediaController && window.vsMediaController.isPlaying) {
        console.log('🚫 鼠标离开：VS模式播放中，忽略离开 (tech-animations.js)');
        return;
      }

      console.log('🎯 鼠标离开：淡化回图片');

      // 直接设置样式，触发CSS过渡效果
      video.style.opacity = '0';
      image.style.opacity = '1';

      // 停止视频
      video.pause();
      video.currentTime = 0;
    };

    // 添加鼠标事件监听器
    console.log('🎯 添加事件监听器到容器:', interactiveContainer);
    interactiveContainer.addEventListener('mouseenter', handleMouseEnter);
    interactiveContainer.addEventListener('mouseleave', handleMouseLeave);
    console.log('✅ 事件监听器添加完成');

    // 视频加载错误处理
    video.addEventListener('error', (e) => {
      console.error('Video loading error:', e);
      // 隐藏视频，只显示图片
      video.style.display = 'none';
      interactiveContainer.classList.add('video-error');

      // 移除交互功能
      interactiveContainer.removeEventListener('mouseenter', handleMouseEnter);
      interactiveContainer.removeEventListener('mouseleave', handleMouseLeave);
    });

    // 视频加载完成
    video.addEventListener('loadeddata', () => {
      console.log('Interactive video loaded successfully');
    });

    // 移动端触摸支持
    if ('ontouchstart' in window) {
      let touchStarted = false;

      interactiveContainer.addEventListener('touchstart', (e) => {
        e.preventDefault();
        touchStarted = true;
        interactiveContainer.classList.add('touch-active');
        video.currentTime = 0;
        video.play().catch(error => {
          console.warn('Video play failed on touch:', error);
        });
      });

      interactiveContainer.addEventListener('touchend', (e) => {
        e.preventDefault();
        if (touchStarted) {
          setTimeout(() => {
            video.pause();
            video.currentTime = 0;
            interactiveContainer.classList.remove('touch-active');
            touchStarted = false;
          }, 2000); // 2秒后自动停止
        }
      });
    }

    // 标记视频已加载
    video.addEventListener('loadeddata', () => {
      video.setAttribute('data-loaded', 'true');
    });

    console.log('Interactive video initialized');
  };

  // 全局调试函数
  window.debugInteractiveVideo = () => {
    console.log('🔧 手动调试交互式视频');
    const container = document.querySelector('.interactive-media-container');
    const video = document.querySelector('.interactive-video');
    const image = document.querySelector('.static-image');

    console.log('元素状态:', {
      container: !!container,
      video: !!video,
      image: !!image,
      videoOpacity: video ? window.getComputedStyle(video).opacity : 'N/A',
      imageOpacity: image ? window.getComputedStyle(image).opacity : 'N/A'
    });

    if (container) {
      console.log('手动触发鼠标进入事件');
      container.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));

      setTimeout(() => {
        console.log('手动触发鼠标离开事件');
        container.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }));
      }, 2000);
    }
  };

  // 初始化交互式视频
  initInteractiveVideo();

  // 开发测试功能已移除
});
