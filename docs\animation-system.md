# 动画系统技术文档

## 概述

本文档详细介绍Shopify Motion主题中的动画系统实现，包括CSS动画、JavaScript交互和性能优化策略。

## 核心动画类

### 1. 基础动画类

#### `.animate-fade-in` - 淡入动画
```css
.animate-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.animate-fade-in.is-visible {
  opacity: 1;
  transform: translateY(0);
}
```

**使用场景**: 文本内容、卡片组件的入场动画

#### `.animate-slide-in` - 滑入动画
```css
.animate-slide-in {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.animate-slide-in.is-visible {
  opacity: 1;
  transform: translateX(0);
}
```

**使用场景**: 导航菜单、侧边栏内容

#### `.animate-float` - 浮动动画
```css
.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}
```

**使用场景**: 装饰性元素、产品图片

### 2. 高级动画效果

#### `.glitch` - 故障风格文字效果
```css
.glitch {
  position: relative;
  color: #fff;
  font-size: 4em;
  letter-spacing: 0.5em;
  animation: glitch-skew 1s infinite linear alternate-reverse;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-anim-1 0.5s infinite linear alternate-reverse;
  color: #ff0000;
  z-index: -1;
}

.glitch::after {
  animation: glitch-anim-2 1s infinite linear alternate-reverse;
  color: #00ffff;
  z-index: -2;
}
```

**使用场景**: 科技感标题、特殊强调文本

## JavaScript动画控制器

### 1. Intersection Observer实现

```javascript
// 滚动触发动画的核心实现
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.classList.add('is-visible');
      
      // 延迟动画支持
      const delay = entry.target.dataset.delay || 0;
      if (delay > 0) {
        setTimeout(() => {
          entry.target.classList.add('is-visible');
        }, delay * 1000);
      }
    }
  });
}, observerOptions);

// 观察所有动画元素
const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-slide-in, .animate-float');
animatedElements.forEach(el => observer.observe(el));
```

### 2. 磁性按钮效果

```javascript
// 磁性按钮交互实现
const magneticButtons = document.querySelectorAll('.magnetic-button');

magneticButtons.forEach(button => {
  button.addEventListener('mousemove', function(e) {
    const rect = button.getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;
    
    // 磁性效果强度控制
    const strength = 0.1;
    button.style.transform = `translate(${x * strength}px, ${y * strength}px)`;
  });

  button.addEventListener('mouseleave', function() {
    button.style.transform = 'translate(0px, 0px)';
  });
});
```

### 3. 视差滚动效果

```javascript
// 视差滚动实现
function initParallax() {
  const parallaxElements = document.querySelectorAll('.parallax-element');
  
  window.addEventListener('scroll', () => {
    const scrollTop = window.pageYOffset;
    
    parallaxElements.forEach(element => {
      const speed = element.dataset.speed || 0.5;
      const yPos = -(scrollTop * speed);
      element.style.transform = `translateY(${yPos}px)`;
    });
  });
}
```

## IR3产品页面特殊动画

### 1. 背景层动画

#### 渐变动画背景
```css
.animated-gradient {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
```

#### 科技线条动画
```css
.tech-lines::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  animation: scan-line 3s linear infinite;
}

@keyframes scan-line {
  0% { left: -100%; }
  100% { left: 100%; }
}
```

### 2. 浮动几何形状

```css
.floating-shapes .shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float-random 6s ease-in-out infinite;
}

.shape-1 {
  width: 60px;
  height: 60px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 40px;
  height: 40px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

@keyframes float-random {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% { 
    transform: translateY(-20px) rotate(120deg);
    opacity: 0.7;
  }
  66% { 
    transform: translateY(10px) rotate(240deg);
    opacity: 0.5;
  }
}
```

### 3. 粒子场效果

```javascript
// 粒子系统实现
class ParticleField {
  constructor(container) {
    this.container = container;
    this.particles = [];
    this.particleCount = 50;
    this.init();
  }

  init() {
    for (let i = 0; i < this.particleCount; i++) {
      this.createParticle();
    }
    this.animate();
  }

  createParticle() {
    const particle = document.createElement('div');
    particle.className = 'particle';
    
    // 随机位置和大小
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    particle.style.width = Math.random() * 4 + 1 + 'px';
    particle.style.height = particle.style.width;
    
    // 随机动画延迟
    particle.style.animationDelay = Math.random() * 3 + 's';
    
    this.container.appendChild(particle);
    this.particles.push(particle);
  }

  animate() {
    // 粒子移动逻辑
    requestAnimationFrame(() => this.animate());
  }
}

// 初始化粒子场
document.addEventListener('DOMContentLoaded', () => {
  const particleContainer = document.querySelector('.particle-field');
  if (particleContainer) {
    new ParticleField(particleContainer);
  }
});
```

## 性能优化策略

### 1. 动画性能优化

#### 使用transform代替position
```css
/* 避免 */
.bad-animation {
  animation: move-bad 1s ease;
}
@keyframes move-bad {
  from { left: 0; }
  to { left: 100px; }
}

/* 推荐 */
.good-animation {
  animation: move-good 1s ease;
}
@keyframes move-good {
  from { transform: translateX(0); }
  to { transform: translateX(100px); }
}
```

#### 使用will-change属性
```css
.animated-element {
  will-change: transform, opacity;
}

.animated-element.animation-complete {
  will-change: auto; /* 动画完成后移除 */
}
```

### 2. JavaScript性能优化

#### 防抖滚动事件
```javascript
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 使用防抖优化滚动事件
const debouncedScrollHandler = debounce(handleScroll, 16); // ~60fps
window.addEventListener('scroll', debouncedScrollHandler);
```

#### 使用requestAnimationFrame
```javascript
function smoothAnimation() {
  // 动画逻辑
  requestAnimationFrame(smoothAnimation);
}

// 启动动画循环
requestAnimationFrame(smoothAnimation);
```

## 响应式动画适配

### 移动端优化
```css
@media (max-width: 589px) {
  /* 简化移动端动画 */
  .animate-fade-in {
    transition-duration: 0.3s; /* 缩短动画时间 */
  }
  
  /* 禁用复杂动画 */
  .complex-animation {
    animation: none;
  }
}

/* 尊重用户的动画偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

### 触摸设备适配
```javascript
// 检测触摸设备并调整动画
if (theme.config.isTouch) {
  // 禁用悬停动画
  document.body.classList.add('touch-device');
  
  // 调整交互方式
  const hoverElements = document.querySelectorAll('.hover-effect');
  hoverElements.forEach(el => {
    el.addEventListener('touchstart', handleTouchInteraction);
  });
}
```

## 调试和测试

### 动画调试工具
```javascript
// 动画性能监控
function monitorAnimationPerformance() {
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'measure') {
        console.log(`Animation: ${entry.name}, Duration: ${entry.duration}ms`);
      }
    });
  });
  
  observer.observe({entryTypes: ['measure']});
}
```

### 浏览器兼容性检测
```javascript
// 检测CSS动画支持
function supportsCSS3Animation() {
  const elm = document.createElement('div');
  const animationSupport = elm.style.animationName !== undefined;
  const transitionSupport = elm.style.transitionProperty !== undefined;
  
  return animationSupport && transitionSupport;
}

// 降级处理
if (!supportsCSS3Animation()) {
  document.body.classList.add('no-animations');
}
```

## 最佳实践

1. **性能优先**: 优先使用transform和opacity属性
2. **渐进增强**: 确保基础功能在不支持动画的环境下正常工作
3. **用户体验**: 尊重用户的动画偏好设置
4. **测试覆盖**: 在不同设备和浏览器上测试动画效果
5. **代码维护**: 保持动画代码的模块化和可维护性

---

*文档版本: v1.0 | 最后更新: 2025-07-07*
