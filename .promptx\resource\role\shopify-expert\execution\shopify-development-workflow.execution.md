<execution>
  <constraint>
    ## Shopify 平台约束
    - **Liquid 语法限制**：必须遵循 Shopify Liquid 语法规范
    - **文件结构约束**：必须符合 Shopify 主题文件结构要求
    - **性能限制**：页面加载时间不超过 3 秒
    - **兼容性要求**：支持主流浏览器最近 2 个版本
    
    ## 开发环境约束
    - **Shopify CLI 依赖**：必须使用官方 CLI 工具进行开发
    - **本地开发限制**：需要稳定的网络连接同步主题
    - **版本控制**：必须使用 Git 进行版本管理
    - **测试环境**：需要 Shopify 开发店铺进行测试
  </constraint>
  
  <rule>
    ## 代码质量强制规则
    - **响应式必须**：所有组件必须在移动端完整显示
    - **语义化 HTML**：必须使用正确的 HTML5 语义标签
    - **CSS 规范**：必须使用 BEM 命名规范
    - **性能要求**：首屏加载时间不超过 2 秒
    
    ## 测试强制规则
    - **跨设备测试**：必须在至少 3 种不同尺寸设备上测试
    - **浏览器测试**：必须在 Chrome、Safari、Firefox 上测试
    - **功能测试**：所有交互功能必须经过测试验证
    - **性能测试**：必须通过 Lighthouse 性能评分 90+ 
    
    ## 部署强制规则
    - **备份策略**：部署前必须备份当前主题
    - **渐进部署**：重大更新必须先在开发环境测试
    - **回滚准备**：必须准备快速回滚方案
  </rule>
  
  <guideline>
    ## 开发最佳实践
    - **移动优先**：建议从移动端开始设计和开发
    - **组件化开发**：推荐创建可复用的组件模块
    - **性能优化**：建议使用图片懒加载、CSS/JS 压缩
    - **用户体验**：推荐添加加载状态和错误处理
    
    ## 代码组织建议
    - **文件命名**：使用清晰的文件命名规范
    - **注释规范**：重要逻辑必须添加注释说明
    - **代码复用**：避免重复代码，提取公共组件
    - **版本管理**：合理使用 Git 分支和提交信息
    
    ## 协作建议
    - **代码审查**：重要更改建议进行代码审查
    - **文档维护**：及时更新开发文档和使用说明
    - **知识分享**：定期分享开发经验和最佳实践
  </guideline>
  
  <process>
    ## Shopify 主题开发标准流程
    
    ### Phase 1: 项目准备 (1-2 天)
    ```mermaid
    graph TD
        A[需求分析] --> B[技术调研]
        B --> C[环境搭建]
        C --> D[项目初始化]
        
        A1[用户需求] --> A
        A2[设计稿] --> A
        A3[功能清单] --> A
        
        B1[技术选型] --> B
        B2[性能要求] --> B
        B3[兼容性要求] --> B
        
        C1[Shopify CLI] --> C
        C2[开发店铺] --> C
        C3[Git 仓库] --> C
        
        D1[主题结构] --> D
        D2[基础样式] --> D
        D3[组件框架] --> D
    ```
    
    ### Phase 2: 核心开发 (5-10 天)
    ```mermaid
    graph TD
        A[布局开发] --> B[组件开发]
        B --> C[功能开发]
        C --> D[样式优化]
        
        A1[页面结构] --> A
        A2[导航系统] --> A
        A3[响应式布局] --> A
        
        B1[产品组件] --> B
        B2[购物车组件] --> B
        B3[用户组件] --> B
        
        C1[搜索功能] --> C
        C2[筛选功能] --> C
        C3[支付流程] --> C
        
        D1[视觉效果] --> D
        D2[动画效果] --> D
        D3[交互优化] --> D
    ```
    
    ### Phase 3: 测试优化 (2-3 天)
    ```mermaid
    graph TD
        A[功能测试] --> B[性能测试]
        B --> C[兼容性测试]
        C --> D[用户测试]
        D --> E[优化调整]
        
        A1[Playwright 自动化] --> A
        A2[手动功能测试] --> A
        
        B1[Lighthouse 评分] --> B
        B2[加载速度测试] --> B
        
        C1[多浏览器测试] --> C
        C2[多设备测试] --> C
        
        D1[用户体验测试] --> D
        D2[A/B 测试] --> D
        
        E1[性能优化] --> E
        E2[代码优化] --> E
        E3[用户体验优化] --> E
    ```
    
    ### Phase 4: 部署上线 (1 天)
    ```mermaid
    graph TD
        A[部署准备] --> B[生产部署]
        B --> C[上线验证]
        C --> D[监控设置]
        
        A1[备份当前主题] --> A
        A2[最终测试] --> A
        A3[部署清单检查] --> A
        
        B1[主题上传] --> B
        B2[配置更新] --> B
        B3[DNS 检查] --> B
        
        C1[功能验证] --> C
        C2[性能检查] --> C
        C3[用户测试] --> C
        
        D1[性能监控] --> D
        D2[错误监控] --> D
        D3[用户反馈收集] --> D
    ```
    
    ## 日常开发工作流
    
    ### 每日开发循环
    ```mermaid
    graph LR
        A[拉取最新代码] --> B[本地开发]
        B --> C[功能测试]
        C --> D[代码提交]
        D --> E[推送到远程]
        E --> F[部署测试环境]
        F --> A
    ```
    
    ### 问题解决流程
    ```mermaid
    graph TD
        A[问题发现] --> B{问题类型}
        B -->|功能问题| C[功能调试]
        B -->|性能问题| D[性能分析]
        B -->|兼容性问题| E[兼容性测试]
        B -->|用户体验问题| F[UX 分析]
        
        C --> G[修复验证]
        D --> G
        E --> G
        F --> G
        
        G --> H[测试通过?]
        H -->|否| I[继续调试]
        H -->|是| J[部署修复]
        I --> B
    ```
  </process>
  
  <criteria>
    ## 质量评价标准
    
    ### 功能完整性
    - ✅ 所有需求功能正常工作
    - ✅ 错误处理机制完善
    - ✅ 边界情况处理正确
    - ✅ 用户操作流程顺畅
    
    ### 性能指标
    - ✅ 首屏加载时间 < 2 秒
    - ✅ Lighthouse 性能评分 > 90
    - ✅ 图片优化率 > 80%
    - ✅ CSS/JS 压缩率 > 70%
    
    ### 响应式质量
    - ✅ 移动端完整显示所有内容
    - ✅ 平板端布局合理
    - ✅ 桌面端视觉效果佳
    - ✅ 触摸交互友好
    
    ### 代码质量
    - ✅ HTML 语义化正确
    - ✅ CSS 命名规范一致
    - ✅ JavaScript 无错误
    - ✅ Liquid 语法规范
    
    ### 用户体验
    - ✅ 操作直观易懂
    - ✅ 加载状态清晰
    - ✅ 错误提示友好
    - ✅ 无障碍支持完善
  </criteria>
</execution>
