/**
 * Hero Section for Ideaformer IR3 V2 3D Printer - Enhanced Version
 * File: assets/IR3-hero-section-1.js
 */

// Enhanced Animations and Interactions
document.addEventListener('DOMContentLoaded', function() {
  const section = document.querySelector('.hero-section');
  
  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('is-visible');
      }
    });
  }, observerOptions);

  // Observe all animated elements
  const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-slide-in, .animate-float');
  animatedElements.forEach(el => observer.observe(el));

  // Smooth scroll for scroll indicator
  const scrollIndicator = document.querySelector('.scroll-indicator');
  if (scrollIndicator) {
    scrollIndicator.addEventListener('click', function() {
      window.scrollTo({
        top: window.innerHeight,
        behavior: 'smooth'
      });
    });
  }

  // Magnetic button effect
  const magneticButtons = document.querySelectorAll('.magnetic-button');
  magneticButtons.forEach(button => {
    button.addEventListener('mousemove', function(e) {
      const rect = button.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;
      
      button.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
    });

    button.addEventListener('mouseleave', function() {
      button.style.transform = 'translate(0, 0)';
    });
  });

  // Parallax effect on mouse move
  let mouseX = 0;
  let mouseY = 0;
  let targetX = 0;
  let targetY = 0;

  document.addEventListener('mousemove', function(e) {
    mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
    mouseY = (e.clientY / window.innerHeight - 0.5) * 2;
  });

  function updateParallax() {
    targetX += (mouseX - targetX) * 0.1;
    targetY += (mouseY - targetY) * 0.1;

    const shapes = document.querySelectorAll('.shape');
    shapes.forEach((shape, index) => {
      const speed = (index + 1) * 10;
      shape.style.transform = `translate(${targetX * speed}px, ${targetY * speed}px)`;
    });

    const productStage = document.querySelector('.product-stage');
    if (productStage && window.innerWidth > 1024) {
      // Limit rotation to subtle movement to complement the keyframe animation
      productStage.style.transform = `rotateY(${targetX * 3}deg) rotateX(${-targetY * 2}deg)`;
    }

    requestAnimationFrame(updateParallax);
  }

  updateParallax();

  // Number animation for spec values
  const specValues = document.querySelectorAll('.spec-value');
  specValues.forEach(value => {
    const originalText = value.textContent;
    value.setAttribute('data-original', originalText);
    
    value.addEventListener('mouseenter', function() {
      value.style.transform = 'scale(1.1)';
    });

    value.addEventListener('mouseleave', function() {
      value.style.transform = 'scale(1)';
    });
  });
});
