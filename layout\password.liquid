<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}" dir="{{ settings.text_direction }}">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.color_button }}">
  <link rel="canonical" href="{{ canonical_url }}">
  <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
  <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>

  {%- if settings.favicon != blank -%}
    <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png" />
  {%- endif -%}

  {%- render 'seo-title' -%}

  {%- if page_description -%}
  <meta name="description" content="{{ page_description | escape }}">
  {%- endif -%}

  {%- render 'social-meta-tags' -%}

  {%- render 'font-face' -%}
  {{ 'theme.css' | asset_url | stylesheet_tag: preload: true }}
  {%- render 'css-variables' -%}

  {{ content_for_header }}

  <script>
    document.documentElement.className = document.documentElement.className.replace('no-js', 'js');

    window.theme = window.theme || {};
    theme.settings = {
      themeName: 'Motion',
      themeVersion: "11.0.0",
      moneyFormat: {{ shop.money_format | json }},
      saveType: {{ settings.product_save_type | json }},
      productImageSize: {{ settings.product_grid_image_size | json }},
      productImageCover: {{ settings.product_grid_image_fill }},
    };
  </script>

  <script src="{{ 'vendor-scripts-v14.js' | asset_url | split: '?' | first }}" defer="defer"></script>
  <script src="{{ 'theme.js' | asset_url }}" defer="defer"></script>
</head>

<body data-transitions="{{ settings.animate_page_transitions }}" data-type_header_capitalize="{{ settings.type_header_capitalize }}" data-type_base_accent_transform="{{ settings.type_base_accent_transform }}" data-type_header_accent_transform="{{ settings.type_header_accent_transform }}" data-animate_sections="{{ settings.animate_sections }}" data-animate_underlines="{{ settings.animate_underlines }}" data-animate_buttons="{{ settings.animate_buttons }}" data-animate_images="{{ settings.animate_images }}" data-animate_page_transition_style="{{ settings.animate_page_transition_style }}" data-type_header_text_alignment="{{ settings.type_headers_align_text }}" data-animate_images_style="{{ settings.animate_images_style | default: "zoom-fade" }}">
  {% if settings.animate_page_transitions %}
    <script type="text/javascript">window.setTimeout(function() { document.body.className += " loaded"; }, 25);</script>
  {% endif %}

  <a class="in-page-link visually-hidden skip-link" href="#MainContent">{{ 'general.accessibility.skip_to_content' | t }}</a>

  <div id="PageContainer" class="page-container">
    <div class="transition-body">
      <main id="MainContent">
        {{ content_for_layout }}
      </main>

      <div class="page-width password__footer text-center">
        {%- capture shopify -%}
          <a href="//www.shopify.com" rel="nofollow" target="_blank" title="Create your own online store with Shopify">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon--wide icon-shopify-logo" viewBox="0 0 150 43"><title>Shopify logo</title><path fill="#999" d="M33.3 8.9s0-.2-.1-.3c-.1-.1-.2-.1-.2-.1l-3.4-.2-2.1-2.1c-.1-.1-.2-.1-.3-.1l-1.8 36.1L38 39.5 33.3 8.9Zm-7.5-3-.9.3c-.6-1.6-1.3-2.8-2.3-3.5-.7-.5-1.5-.7-2.3-.6l-.6-.6c-.9-.7-2.1-.9-3.6-.3C11.8 2.7 10 8.3 9.3 11l-3.8 1.1s-.9.2-1.1.5c-.2.3-.3 1-.3 1L.9 37.9l23.6 4.4L26.3 6c-.2-.2-.4-.1-.5-.1Zm-5.7 1.7L16 8.9c.5-2.1 1.6-4.3 3.6-5.1.4 1 .5 2.5.5 3.8Zm-3.5-5.2c.9-.3 1.6-.3 2.1 0-2.7 1.2-3.9 4.3-4.4 6.9l-3.3 1c.7-2.5 2.3-6.7 5.6-7.9Zm2.3 17.9c-.2-.1-.4-.2-.7-.3-.3-.1-.5-.2-.8-.3-.3-.1-.6-.1-1-.2h-1.1c-.3 0-.6.1-.9.2-.3.1-.5.2-.7.4-.2.2-.3.4-.4.6-.1.2-.2.5-.2.7 0 .2 0 .4.1.6l.3.6.6.6c.2.2.5.4.8.6.5.3.9.6 1.4 1 .5.4.9.8 1.2 1.3.4.5.7 1 .9 1.7.2.6.3 1.3.3 2.1-.1 1.2-.3 2.3-.8 3.2-.4.9-1.1 1.6-1.8 2.1s-1.6.8-2.5.9c-.9.1-1.9.1-2.8-.2-.5-.1-.9-.3-1.3-.4l-1.2-.6c-.3-.2-.7-.4-.9-.6-.3-.2-.5-.4-.7-.7L7.8 30c.2.2.4.3.7.5.3.2.6.4.9.5.3.2.7.3 1 .5.4.1.7.2 1.1.3h.8c.2-.1.5-.2.6-.3.2-.1.3-.3.4-.5.1-.2.1-.4.2-.7 0-.2 0-.5-.1-.7-.1-.2-.2-.4-.3-.7-.1-.2-.3-.4-.6-.7-.2-.2-.5-.5-.9-.7-.4-.3-.8-.6-1.2-1-.3-.4-.7-.7-.9-1.2-.2-.4-.4-.9-.6-1.4-.1-.5-.2-1-.2-1.6 0-1 .2-1.8.6-2.6.3-.8.8-1.5 1.4-2.2.6-.6 1.3-1.2 2.2-1.6.9-.4 1.8-.7 2.9-.9.5-.1 1-.1 1.4-.1.5 0 .9 0 1.3.1s.8.1 1.1.2l.9.3-1.6 4.8Zm2.6-13.1v-.5c0-1.3-.2-2.4-.5-3.2.3 0 .6.1.9.3.8.5 1.3 1.6 1.7 2.8l-2.1.6Zm23.8 22.4c.9.5 2.5 1.1 4.1 1.1 1.4 0 2.2-.8 2.2-1.7 0-.9-.5-1.5-2.1-2.4-1.9-1.1-3.3-2.6-3.3-4.6 0-3.5 3-6 7.4-6 1.9 0 3.4.4 4.2.8l-1.2 3.5c-.7-.3-1.8-.7-3.1-.7-1.4 0-2.3.6-2.3 1.7 0 .8.7 1.4 1.9 2 2 1.1 3.6 2.6 3.6 4.7 0 4-3.2 6.2-7.7 6.1-2.1 0-4-.6-4.9-1.2l1.2-3.3Zm12.4 4.5 4.9-25.2h5l-1.9 9.8h.1c1.3-1.6 3.1-2.7 5.3-2.7 2.6 0 4.1 1.7 4.1 4.5 0 .9-.1 2.2-.4 3.3l-2 10.3h-5l1.9-9.9c.1-.7.2-1.5.2-2.2 0-1.1-.4-1.8-1.6-1.8-1.6 0-3.3 2-4 5.3l-1.7 8.7h-4.9v-.1ZM93.3 23c0 6.1-4 11.4-9.9 11.4-4.5 0-6.9-3.1-6.9-6.9 0-6 4-11.4 10-11.4 4.7 0 6.8 3.3 6.8 6.9Zm-11.7 4.3c0 1.8.7 3.2 2.4 3.2 2.7 0 4.1-4.7 4.1-7.7 0-1.5-.6-3-2.4-3-2.6.1-4.1 4.7-4.1 7.5Zm10.5 13.8L95.6 23c.4-2 .8-4.7 1-6.6h4.4l-.3 2.8h.1c1.3-1.9 3.3-3 5.3-3 3.7 0 5.2 2.9 5.2 6.3 0 6-3.9 12.1-9.7 12.1-1.2 0-2.4-.5-2.9-.5h-.1l-1.4 7h-5.1Zm7.2-11.2c.5.4 1.2.7 2.1.7 2.8 0 4.7-4.6 4.7-7.8 0-1.3-.5-2.7-2-2.7-1.7 0-3.4 2-4 5.1l-.8 4.7Zm12.2 4.2 3.4-17.7h5.1l-3.4 17.7h-5.1Zm6.5-19.6c-1.4 0-2.4-1.1-2.4-2.6 0-1.6 1.3-2.9 2.9-2.9 1.5 0 2.5 1.1 2.5 2.6 0 1.8-1.4 2.9-3 2.9Zm2.9 19.6 2.7-14h-2.3l.7-3.7h2.3l.1-.8c.4-2.1 1.2-4.2 2.9-5.6 1.3-1.1 3.1-1.6 4.9-1.6 1.2 0 2.1.2 2.7.4l-1 3.9c-.4-.1-.9-.3-1.6-.3-1.7 0-2.7 1.5-3 3.2l-.2.8h3.5l-.7 3.7h-3.5l-2.7 14h-4.8Zm18-17.7.8 7.9c.2 1.8.4 3.3.4 4.2h.1c.4-.9.8-2.3 1.5-4.2l3.1-7.9h5.2l-6.1 13.1c-2.2 4.5-4.3 7.7-6.6 9.9-1.8 1.7-3.9 2.5-4.9 2.7l-1.4-4.2c.8-.3 1.9-.7 2.8-1.4 1.2-.8 2.1-1.9 2.7-3 .1-.3.2-.5.1-1.9l-3-15.2h5.3Z"/></svg>
            <span class="icon__fallback-text">Shopify</span>
          </a>
        {%- endcapture -%}
        {{ 'general.password_page.powered_by_shopify_html' | t: shopify: shopify }}
      </div>
    </div>
  </div>

  <div id="LoginModal" class="modal modal--solid">
    <div class="modal__inner">
      <div class="modal__centered text-center">
        <div class="password-form">
          <p class="h4">{{ 'general.password_page.login_form_heading' | t }}</p>
          {% form 'storefront_password' %}
            {{ form.errors | default_errors }}
            <label for="password" class="hidden-label">{{ 'general.password_page.login_form_password_label' | t }}</label>
            <label for="password-submit" class="hidden-label">{{ 'general.password_page.login_form_submit' | t }}</label>
            <div class="input-group">
              <input type="password" name="password" id="password" class="input-group-field" placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}" autofocus>
              <span class="input-group-btn">
                <input type="submit" id="password-submit" class="btn btn--no-animate password-form__submit" value="{{ 'general.password_page.login_form_submit' | t }}">
              </span>
            </div>
          {% endform %}
        </div>
        <p class="password-admin-link">
          <small>
            {{ 'general.password_page.admin_link_html' | t }}
          </small>
        </p>
      </div>
    </div>

    <button type="button" class="modal__close js-modal-close text-link">
      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-close" viewBox="0 0 64 64"><title>icon-X</title><path d="m19 17.61 27.12 27.13m0-27.12L19 44.74"/></svg>
      <span class="icon__fallback-text">{{ 'general.accessibility.close_modal' | t | json }}</span>
    </button>
  </div>

  {%- liquid
    section 'newsletter-popup'
    render 'video-modal'
    render 'photoswipe-template'
  -%}
</body>
</html>
