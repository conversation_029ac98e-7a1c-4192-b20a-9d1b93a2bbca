{% comment %}
  IR3 Parameter Display Section - Technical Specifications Showcase (Table View)
  File: sections/ir3-parameter-display.liquid
{% endcomment %}

{{ 'ir3-parameter-display.css' | asset_url | stylesheet_tag }}

<section
  class="param-display-section"
  id="param-display-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Parameter Display Container -->
  <div class="param-display-container">
    <!-- Enhanced Background Layers -->
    <div class="param-display-background">
      <div class="param-display-gradient-overlay"></div>
      <div class="param-display-animated-gradient"></div>
      <div class="param-display-grid-pattern"></div>
      <div class="param-display-tech-lines"></div>
      <div class="param-display-floating-shapes">
        <div class="param-display-shape param-display-shape-1"></div>
        <div class="param-display-shape param-display-shape-2"></div>
        <div class="param-display-shape param-display-shape-3"></div>
      </div>
    </div>

    <!-- Content Layer -->
    <div class="param-display-content-layer">
      <div class="param-display-wrapper">
        <!-- Header Section -->
        <div class="param-display-header">
          <div class="param-display-title-group">
            <span class="param-display-pre-title">{{ section.settings.pre_title | default: 'Technical Specifications' }}</span>
            <h2 class="param-display-main-title">
              {{ section.settings.main_title | default: 'IR3 V2 Parameters' }}
            </h2>
            <p class="param-display-description">
              {{ section.settings.description | default: 'Comprehensive technical specifications and features overview' }}
            </p>
          </div>
        </div>

        <!-- Category Navigation -->
        <div class="param-display-nav">
          <button class="param-display-nav-btn active" data-category="machine-parameters">
            <span class="param-display-nav-icon">⚙️</span>
            <span class="param-display-nav-text">Machine Parameters</span>
            <span class="param-display-nav-count">26</span>
          </button>
          <button class="param-display-nav-btn" data-category="sensors">
            <span class="param-display-nav-icon">📡</span>
            <span class="param-display-nav-text">Sensors</span>
            <span class="param-display-nav-count">7</span>
          </button>
          <button class="param-display-nav-btn" data-category="electrical-hardware">
            <span class="param-display-nav-icon">⚡</span>
            <span class="param-display-nav-text">Electrical Hardware</span>
            <span class="param-display-nav-count">6</span>
          </button>
          <button class="param-display-nav-btn" data-category="software">
            <span class="param-display-nav-icon">💻</span>
            <span class="param-display-nav-text">Software</span>
            <span class="param-display-nav-count">4</span>
          </button>
        </div>

        <!-- Parameters Content -->
        <div class="param-display-content">
          <!-- Machine Parameters -->
          <div class="param-display-category active" data-category="machine-parameters">
            <!-- Table will be generated by JavaScript -->
          </div>

          <!-- Sensors Category -->
          <div class="param-display-category" data-category="sensors">
            <!-- Table will be generated by JavaScript -->
          </div>

          <!-- Electrical Hardware Category -->
          <div class="param-display-category" data-category="electrical-hardware">
            <!-- Table will be generated by JavaScript -->
          </div>

          <!-- Software Category -->
          <div class="param-display-category" data-category="software">
            <!-- Table will be generated by JavaScript -->
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{{ 'ir3-parameter-display.js' | asset_url | script_tag }}

<script>
// 确保参数显示自动初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 参数显示自动初始化开始');

  // 等待一小段时间确保所有元素都已加载
  setTimeout(function() {
    const section = document.querySelector('.param-display-section');
    if (!section) {
      console.log('❌ 未找到参数显示组件');
      return;
    }

    console.log('✅ 找到参数显示组件，开始初始化');

    const navButtons = section.querySelectorAll('.param-display-nav-btn');
    const categories = section.querySelectorAll('.param-display-category');

    // 完整的参数数据
    const data = {
      'machine-parameters': [
        { name: 'Printing Technology', value: 'FDM' },
        { name: 'Machine Structure', value: 'Full metal frame' },
        { name: 'Motion Structure', value: 'CoreXY' },
        { name: 'Filament Diameter', value: '1.75mm' },
        { name: 'Motor Type', value: '5:1 Dual gear reduction extruder motor' },
        { name: 'Nozzle Material', value: 'Hardened steel' },
        { name: 'Nozzle Size', value: 'Standard 0.4mm' },
        { name: 'Print Volume', value: '250×250×∞mm (X*Y*Z)' },
        { name: 'Product Dimensions', value: '676×436×510mm' },
        { name: 'Package Dimensions', value: '770×510×320mm' },
        { name: 'Net Weight', value: '16.5kg' },
        { name: 'Gross Weight', value: '21kg' },
        { name: 'Print Precision', value: '±0.1mm' },
        { name: 'Layer Thickness', value: '0.1-0.3mm' },
        { name: 'Print Speed', value: '≤400mm/s' },
        { name: 'Print Acceleration', value: '≤20000mm/s²' },
        { name: 'Maximum Nozzle Temperature', value: '300°C' },
        { name: 'Maximum Heated Bed Temperature', value: '90°C' },
        { name: 'Nozzle Heating Time', value: '40s' },
        { name: 'Heated Bed Heating Time', value: '90s' },
        { name: 'Maximum Flow Rate', value: '26mm³/s' },
        { name: 'Print Platform', value: 'PEI metal build surface' },
        { name: 'Compatible Materials', value: 'PLA/PETG/TPU/ABS/ASA, etc.' },
        { name: 'Operating Environment Temperature', value: '10-40°C' },
        { name: 'Noise Level', value: '54dB' },
        { name: 'Print Methods', value: 'USB drive/Local network/Internet network' }
      ],
      'sensors': [
        { name: 'Vibration Compensation', value: '✅' },
        { name: 'Filament Runout Detection', value: '✅' },
        { name: 'Material Shortage Detection', value: '✅' },
        { name: 'Clogging Detection', value: '✅' },
        { name: 'Auto Leveling', value: '✅' },
        { name: 'LED Lighting', value: '✅' },
        { name: 'Camera', value: '✅' }
      ],
      'electrical-hardware': [
        { name: 'Input Voltage', value: '110VAC/220VAC, 50/60Hz' },
        { name: 'Maximum Power', value: '800W' },
        { name: 'Main Controller', value: '64-bit 1.5GHz Quad-core Cortex-A53 processor' },
        { name: 'Memory', value: '16GB-SD, 1GB DDR3' },
        { name: 'User Interface', value: '4.3-inch touchscreen with 800×480 resolution' },
        { name: 'Printing Firmware', value: 'Klipper' }
      ],
      'software': [
        { name: 'Slicing Software', value: 'Ideamaker/Ideaformer Cura' },
        { name: 'Input Formats', value: '.stl/.obj/.3mf/.step/.stp/.iges/.igs/.oltp/.jpg/.jpeg/.png/.bmp' },
        { name: 'Operating Systems', value: 'Windows/MacOS/Linux' },
        { name: 'Output File Format', value: '.gcode' }
      ]
    };

    // 获取值列标题的函数
    function getValueColumnHeader(categoryKey) {
      const headers = {
        'machine-parameters': 'Specification',
        'sensors': 'Support',
        'electrical-hardware': 'Specification',
        'software': 'Specification'
      };
      return headers[categoryKey] || 'Value';
    }

    // 为所有分类生成表格
    categories.forEach(category => {
      const categoryKey = category.dataset.category;
      const categoryData = data[categoryKey];

      if (categoryData) {
        let tableHTML = '<div class="param-table"><div class="param-table-header"><div class="param-table-cell param-table-cell-name">Parameter</div><div class="param-table-cell param-table-cell-value">' + getValueColumnHeader(categoryKey) + '</div></div>';

        categoryData.forEach((item, index) => {
          const rowClass = 'param-table-row ' + (index % 2 === 0 ? 'even' : 'odd');
          tableHTML += '<div class="' + rowClass + '"><div class="param-table-cell param-table-cell-name">' + item.name + '</div><div class="param-table-cell param-table-cell-value">' + item.value + '</div></div>';
        });

        tableHTML += '</div>';
        category.innerHTML = tableHTML;
      }
    });

    // 添加导航功能
    function switchCategory(targetCategory) {
      navButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.category === targetCategory) {
          btn.classList.add('active');
        }
      });

      categories.forEach(category => {
        if (category.dataset.category === targetCategory) {
          category.style.display = 'block';
          category.classList.add('active');
        } else {
          category.classList.remove('active');
          category.style.display = 'none';
        }
      });
    }

    // 为每个导航按钮添加点击事件
    navButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        const targetCategory = button.dataset.category;
        switchCategory(targetCategory);
      });
    });

    // 确保Machine Parameters是默认激活的
    switchCategory('machine-parameters');

    console.log('✅ 参数显示自动初始化完成');
  }, 500);
});
</script>

{% schema %}
{
  "name": "IR3 Parameter Display",
  "tag": "section",
  "class": "ir3-parameter-display-section",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "pre_title",
      "label": "Pre Title",
      "default": "Technical Specifications"
    },
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "IR3 V2 Parameters"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "Comprehensive technical specifications and features overview"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0,
      "info": "输入精确的上边距像素值"
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0,
      "info": "输入精确的下边距像素值"
    }
  ],
  "presets": [
    {
      "name": "IR3 Parameter Display"
    }
  ]
}
{% endschema %}
