/**
 * IR3 Smart Features - Apple Style Horizontal Card Carousel
 * File: assets/ir3-smart-features.js
 * Simplified ScrollTrigger implementation based on demo/scroll.html
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 IR3 Smart Features initializing...');

  // Check GSAP and ScrollTrigger availability
  if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
    console.error('❌ GSAP or ScrollTrigger not available');
    return;
  }

  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);
  console.log('✅ GSAP and ScrollTrigger available and registered');

  // Configuration
  const CONFIG = {
    TOTAL_CARDS: 5,
    ANIMATION_DURATION: 800,
    KEYBOARD_ENABLED: true,
    TOUCH_ENABLED: true,
    SCROLL_DISTANCE: 4000 // Total scroll distance for all cards (800px per card)
  };

  // Simplified state management
  let currentCardIndex = 0;
  let viewedCards = new Set([0]);
  let isAnimating = false;
  let scrollTriggerInstance = null;

  // Touch handling
  let touchStartX = 0;
  let touchStartY = 0;
  let touchEndX = 0;
  let touchEndY = 0;

  // DOM elements
  const smartSection = document.querySelector('.smart-features-section');
  const cardsContainer = document.querySelector('.cards-container');
  const cards = document.querySelectorAll('.feature-card');
  const indicators = document.querySelectorAll('.indicator');
  const prevButton = document.querySelector('.prev-arrow');
  const nextButton = document.querySelector('.next-arrow');
  const completionIndicator = document.querySelector('.completion-indicator');

  if (!smartSection || !cardsContainer || !cards.length) {
    console.error('❌ Smart Features elements not found');
    return;
  }

  console.log(`✅ Found ${cards.length} cards, ${indicators.length} indicators`);

  // Initialize videos
  function initializeVideos() {
    const videos = document.querySelectorAll('.smart-features-section video');
    videos.forEach((video, index) => {
      video.addEventListener('loadeddata', () => {
        console.log(`📹 Video ${index} loaded successfully`);
      });

      video.addEventListener('error', (e) => {
        console.error(`❌ Video ${index} failed to load:`, e);
      });

      // Set video properties
      video.muted = true;
      video.loop = true;
      video.playsInline = true;
    });
  }

  // Update card display with smooth animations
  function updateCardDisplay(newIndex) {
    // Use global variables for state management
    if (window.smartFeaturesIsAnimating || newIndex === window.smartFeaturesCurrentCardIndex) return;

    window.smartFeaturesIsAnimating = true;
    window.smartFeaturesCurrentCardIndex = newIndex;
    currentCardIndex = newIndex; // Keep local variable in sync

    // Remove all show-card classes
    cardsContainer.classList.remove('show-card-0', 'show-card-1', 'show-card-2', 'show-card-3', 'show-card-4');

    // Add current card class
    cardsContainer.classList.add(`show-card-${currentCardIndex}`);

    // Update active states
    cards.forEach((card, index) => {
      card.classList.toggle('active', index === currentCardIndex);
    });

    // Update indicators
    indicators.forEach((indicator, index) => {
      indicator.classList.toggle('active', index === currentCardIndex);
    });

    // Mark card as viewed
    viewedCards.add(currentCardIndex);

    // Control video playback
    controlVideoPlayback();

    console.log(`🎨 Updated display to card ${currentCardIndex}, viewed: [${Array.from(viewedCards).join(', ')}]`);

    // Show completion indicator when all cards viewed
    if (viewedCards.size >= CONFIG.TOTAL_CARDS) {
      console.log('🎯 All cards viewed!');
      showCompletionIndicator();
    }

    // Reset animation flag after transition
    setTimeout(() => {
      window.smartFeaturesIsAnimating = false;
      isAnimating = false; // Keep local variable in sync
    }, CONFIG.ANIMATION_DURATION);
  }

  // Control video playback
  function controlVideoPlayback() {
    cards.forEach((card, index) => {
      const video = card.querySelector('video');
      if (video) {
        if (index === currentCardIndex) {
          video.play().catch(e => console.log('Video autoplay prevented:', e));
        } else {
          video.pause();
        }
      }
    });
  }

  // Navigate to specific card
  function navigateToCard(index) {
    if (index < 0 || index >= CONFIG.TOTAL_CARDS || index === currentCardIndex || isAnimating) {
      return;
    }
    updateCardDisplay(index);
  }

  // Navigate to next card
  function nextCard() {
    const nextIndex = (currentCardIndex + 1) % CONFIG.TOTAL_CARDS;
    navigateToCard(nextIndex);
  }

  // Navigate to previous card
  function prevCard() {
    const prevIndex = (currentCardIndex - 1 + CONFIG.TOTAL_CARDS) % CONFIG.TOTAL_CARDS;
    navigateToCard(prevIndex);
  }

  // Show completion indicator
  function showCompletionIndicator() {
    if (completionIndicator) {
      completionIndicator.style.display = 'block';
      setTimeout(() => {
        completionIndicator.classList.add('show');
      }, 100);
      console.log('✅ All cards viewed - completion indicator shown');
    }
  }


  // Simplified ScrollTrigger setup based on demo/scroll.html
  function setupScrollTrigger() {
    // Skip on mobile devices
    if (isMobileDevice()) {
      console.log('📱 Mobile device detected - skipping Smart Features scroll lock');
      return;
    }

    console.log('🎬 Setting up simplified GSAP ScrollTrigger for Smart Features');

    // Create a timeline for card transitions
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: smartSection,
        pin: true,
        start: "top top",
        end: `+=${CONFIG.SCROLL_DISTANCE}`, // 4000px for 5 cards
        scrub: 1, // Smooth scroll sync
        onUpdate: (self) => {
          // Calculate current card based on scroll progress with better distribution
          const progress = self.progress;

          // Use more precise calculation to ensure smooth transitions
          // Each card gets exactly 20% of the scroll progress (1/5 = 0.2)
          let cardIndex;
          if (progress < 0.2) {
            cardIndex = 0;
          } else if (progress < 0.4) {
            cardIndex = 1;
          } else if (progress < 0.6) {
            cardIndex = 2;
          } else if (progress < 0.8) {
            cardIndex = 3;
          } else {
            cardIndex = 4;
          }

          console.log('🔄 ScrollTrigger onUpdate:', {
            progress: progress.toFixed(3),
            cardIndex,
            currentCardIndex: window.smartFeaturesCurrentCardIndex,
            progressRange: `${(cardIndex * 0.2).toFixed(1)}-${((cardIndex + 1) * 0.2).toFixed(1)}`
          });

          // Update card display if changed
          if (cardIndex !== window.smartFeaturesCurrentCardIndex) {
            updateCardDisplay(cardIndex);
          }
        },
        onComplete: () => {
          console.log('🎯 ScrollTrigger completed - all cards viewed');
        }
      }
    });

    // Store the ScrollTrigger instance
    scrollTriggerInstance = tl.scrollTrigger;

    // Expose updateCardDisplay and state variables to global scope for ScrollTrigger callback
    window.smartFeaturesUpdateCard = updateCardDisplay;
    window.smartFeaturesCurrentCardIndex = currentCardIndex;
    window.smartFeaturesIsAnimating = isAnimating;

    // Initialize first card display
    updateCardDisplay(0);

    console.log('✅ Simplified ScrollTrigger created successfully');
  }

  // Touch event handlers for mobile
  function handleTouchStart(event) {
    if (!CONFIG.TOUCH_ENABLED) return;
    const touch = event.touches[0];
    touchStartX = touch.clientX;
    touchStartY = touch.clientY;
  }

  function handleTouchEnd(event) {
    if (!CONFIG.TOUCH_ENABLED || isAnimating) return;
    const touch = event.changedTouches[0];
    touchEndX = touch.clientX;
    touchEndY = touch.clientY;

    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;

    // Only handle horizontal swipes on mobile
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        prevCard();
      } else {
        nextCard();
      }
    }
  }

  // Keyboard navigation
  function handleKeyboard(event) {
    if (!CONFIG.KEYBOARD_ENABLED) return;

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        prevCard();
        break;
      case 'ArrowRight':
        event.preventDefault();
        nextCard();
        break;
    }
  }

  // Event listeners setup
  function setupEventListeners() {
    // Button navigation
    if (prevButton) {
      prevButton.addEventListener('click', prevCard);
    }

    if (nextButton) {
      nextButton.addEventListener('click', nextCard);
    }

    // Indicator navigation
    indicators.forEach((indicator, index) => {
      indicator.addEventListener('click', () => {
        navigateToCard(index);
      });
    });

    // Keyboard navigation
    if (CONFIG.KEYBOARD_ENABLED) {
      document.addEventListener('keydown', handleKeyboard);
    }

    // Touch navigation for mobile
    if (CONFIG.TOUCH_ENABLED) {
      smartSection.addEventListener('touchstart', handleTouchStart, { passive: true });
      smartSection.addEventListener('touchend', handleTouchEnd, { passive: true });
    }
  }

  // Mobile device detection
  function isMobileDevice() {
    const isSmallScreen = window.innerWidth <= 480;
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isMobileUserAgent = /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const shouldDisable = isSmallScreen && (isTouchDevice || isMobileUserAgent);

    console.log('📱 Mobile detection:', {
      screenWidth: window.innerWidth,
      isSmallScreen,
      isTouchDevice,
      isMobileUserAgent,
      shouldDisable
    });

    return shouldDisable;
  }

  // Setup scroll appearance animations
  function setupScrollAnimations() {
    const section = smartSection;
    const title = section.querySelector('.smart-features-title');
    const subtitle = section.querySelector('.subtitle');
    const cardsCarousel = section.querySelector('.cards-carousel');
    const navigationControls = section.querySelector('.navigation-controls');
    const progressIndicators = section.querySelector('.progress-indicators');

    // Check if component is already visible
    const rect = section.getBoundingClientRect();
    const isAlreadyVisible = rect.top < window.innerHeight * 0.8;

    if (isAlreadyVisible) {
      // Show immediately if already visible
      [title, subtitle, cardsCarousel, navigationControls, progressIndicators].forEach(element => {
        if (element) {
          element.style.opacity = '1';
          element.style.transform = 'translateY(0px)';
          element.style.visibility = 'visible';
        }
      });
      console.log('✅ Smart Features already visible, showing immediately');
      return;
    }

    // Set initial hidden state
    [title, subtitle, cardsCarousel].forEach(element => {
      if (element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(50px)';
        element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
      }
    });

    [navigationControls, progressIndicators].forEach(element => {
      if (element) {
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.5s ease-out';
      }
    });

    // Create ScrollTrigger animation for appearance
    if (typeof gsap !== 'undefined' && typeof ScrollTrigger !== 'undefined') {
      ScrollTrigger.create({
        trigger: section,
        start: 'top 80%',
        onEnter: () => {
          // Show elements sequentially
          setTimeout(() => {
            if (title) {
              title.style.opacity = '1';
              title.style.transform = 'translateY(0px)';
            }
          }, 0);

          setTimeout(() => {
            if (subtitle) {
              subtitle.style.opacity = '1';
              subtitle.style.transform = 'translateY(0px)';
            }
          }, 200);

          setTimeout(() => {
            if (cardsCarousel) {
              cardsCarousel.style.opacity = '1';
              cardsCarousel.style.transform = 'translateY(0px)';
            }
          }, 400);

          setTimeout(() => {
            [navigationControls, progressIndicators].forEach(element => {
              if (element) {
                element.style.opacity = '1';
              }
            });
          }, 600);

          console.log('✅ Smart Features scroll animation triggered');
        },
        once: true
      });
    } else {
      // Fallback if GSAP not available
      [title, subtitle, cardsCarousel, navigationControls, progressIndicators].forEach(element => {
        if (element) {
          element.style.opacity = '1';
          element.style.transform = 'translateY(0px)';
        }
      });
    }

    console.log('✅ Smart Features scroll animations setup complete');
  }

  // Initialize everything
  function initialize() {
    console.log('🎬 Initializing Smart Features...');
    console.log('� Initial state check:', {
      smartSection: !!smartSection,
      cardsContainer: !!cardsContainer,
      cardsCount: cards.length,
      indicatorsCount: indicators.length,
      isMobile: isMobileDevice(),
      screenSize: `${window.innerWidth}x${window.innerHeight}`
    });

    initializeVideos();
    updateCardDisplay(0); // Initialize with first card
    setupEventListeners();
    setupScrollTrigger(); // Use simplified GSAP ScrollTrigger
    setupScrollAnimations(); // Add scroll appearance animations

    console.log('✅ Smart Features initialized successfully');
  }

  // Mobile horizontal scroll functionality
  function initializeMobileScroll() {
    if (window.innerWidth > 768) return; // Only for mobile

    const cardFeatures = document.querySelectorAll('.card-features');

    cardFeatures.forEach(container => {
      if (!container) return;

      const items = container.querySelectorAll('li');
      if (items.length === 0) return;

      // Clone items for seamless loop
      items.forEach(item => {
        const clone = item.cloneNode(true);
        container.appendChild(clone);
      });

      let scrollPosition = 0;
      const itemWidth = items[0].offsetWidth + 8; // item width + gap
      const totalWidth = itemWidth * items.length;
      let isScrolling = false;

      // Auto scroll function
      function autoScroll() {
        if (isScrolling) return;

        scrollPosition += 1;
        container.scrollLeft = scrollPosition;

        // Reset to beginning for seamless loop
        if (scrollPosition >= totalWidth) {
          setTimeout(() => {
            container.scrollLeft = 0;
            scrollPosition = 0;
          }, 100);
        }

        requestAnimationFrame(autoScroll);
      }

      // Pause auto scroll on user interaction
      container.addEventListener('touchstart', () => {
        isScrolling = true;
      });

      container.addEventListener('touchend', () => {
        setTimeout(() => {
          isScrolling = false;
        }, 2000); // Resume after 2 seconds
      });

      container.addEventListener('scroll', () => {
        if (!isScrolling) {
          scrollPosition = container.scrollLeft;
        }
      });

      // Start auto scroll
      autoScroll();
    });
  }

  // Mobile horizontal scroll functionality
  function initializeMobileScroll() {
    if (window.innerWidth > 768) return; // Only for mobile

    const cardFeatures = document.querySelectorAll('.card-features');

    cardFeatures.forEach(container => {
      if (!container) return;

      const items = container.querySelectorAll('li');
      if (items.length === 0) return;

      // Clone items for seamless loop
      items.forEach(item => {
        const clone = item.cloneNode(true);
        container.appendChild(clone);
      });

      let scrollPosition = 0;
      const itemWidth = items[0].offsetWidth + 8; // item width + gap
      const totalWidth = itemWidth * items.length;
      let isScrolling = false;

      // Auto scroll function
      function autoScroll() {
        if (isScrolling) return;

        scrollPosition += 1;
        container.scrollLeft = scrollPosition;

        // Reset to beginning for seamless loop
        if (scrollPosition >= totalWidth) {
          setTimeout(() => {
            container.scrollLeft = 0;
            scrollPosition = 0;
          }, 100);
        }

        requestAnimationFrame(autoScroll);
      }

      // Pause auto scroll on user interaction
      container.addEventListener('touchstart', () => {
        isScrolling = true;
      });

      container.addEventListener('touchend', () => {
        setTimeout(() => {
          isScrolling = false;
        }, 2000); // Resume after 2 seconds
      });

      container.addEventListener('scroll', () => {
        if (!isScrolling) {
          scrollPosition = container.scrollLeft;
        }
      });

      // Start auto scroll
      autoScroll();
    });
  }

  // Initialize everything
  function initialize() {
    console.log('🎬 Initializing Smart Features...');
    console.log('🔍 Initial state check:', {
      smartSection: !!smartSection,
      cardsContainer: !!cardsContainer,
      cardsCount: cards.length,
      indicatorsCount: indicators.length,
      isMobile: isMobileDevice(),
      screenSize: `${window.innerWidth}x${window.innerHeight}`
    });

    initializeVideos();
    updateCardDisplay(0); // Initialize with first card
    setupEventListeners();
    setupScrollTrigger(); // Use simplified GSAP ScrollTrigger
    setupScrollAnimations(); // Add scroll appearance animations

    console.log('✅ Smart Features initialized successfully');
  }

  // Start initialization
  initialize();

  // Initialize mobile scroll on load and resize
  initializeMobileScroll();
  window.addEventListener('resize', initializeMobileScroll);
});

