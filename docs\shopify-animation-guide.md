# Shopify 动画技术指南

## 🎯 概述

本文档详细介绍了 Shopify 主题中使用的动画技术，包括 GSAP 动画库的应用、滚动动画、特性切换动画等。

## 🛠️ 技术栈

### 1. GSAP (GreenSock Animation Platform)
- **版本**: 3.x
- **用途**: 高性能 JavaScript 动画库
- **优势**: 
  - 60fps 流畅动画
  - 跨浏览器兼容
  - 丰富的缓动函数
  - 时间线控制

### 2. ScrollTrigger
- **用途**: 滚动触发动画
- **功能**: 监听滚动位置，触发相应动画

## 📚 核心动画类型

### 1. 特性切换动画 (Feature Switching)

#### 图片动画
```javascript
// 位置: changeFeature 函数中
tl.to(featureImages, {
  opacity: index => (index === currentIndex ? 1 : 0.15),
  scale: index => {
    if (index === currentIndex) return 1.3;    // 当前图片放大
    if (index === previousIndex || index === nextIndex) return 0.7;  // 相邻图片缩小
    return 0;  // 其他图片隐藏
  },
  x: index => {
    if (index === currentIndex) return 0;      // 当前图片居中
    if (index === previousIndex) return '-30%'; // 前一张左移
    if (index === nextIndex) return '30%';     // 后一张右移
    return 0;
  },
  rotateY: index => {
    if (index === currentIndex) return 0;      // 当前图片不旋转
    if (index === previousIndex) return '-15deg'; // 前一张左转
    if (index === nextIndex) return '15deg';   // 后一张右转
    return 0;
  },
  filter: index => (index === currentIndex ? 'blur(0) brightness(1)' : 'blur(3px) brightness(0.6)'),
  duration: 1.0,        // 🎛️ 动画时长（秒）
  ease: "power2.out",   // 🎛️ 缓动函数
  stagger: 0.0          // 🎛️ 交错延迟
});
```

#### 文字动画
```javascript
// 位置: changeFeature 函数中
tl.fromTo(featureTexts[currentIndex],
  {
    opacity: 0,     // 起始状态：透明
    x: -10,         // 起始状态：左移10px
    scale: 0.98     // 起始状态：稍微缩小
  },
  {
    opacity: 1,     // 结束状态：完全显示
    x: 0,           // 结束状态：原位置
    scale: 1,       // 结束状态：原大小
    duration: 1.0,  // 🎛️ 动画时长（秒）
    ease: "power2.out"  // 🎛️ 缓动函数
  },
  "<"  // 🎛️ 时间偏移：与前一个动画同时开始
);
```

### 2. 滚动动画 (Scroll Animations)

#### Initialize scroll animations
```javascript
// 位置: 文件底部的初始化代码
function initializeScrollAnimations() {
  // 检查 GSAP 和 ScrollTrigger 是否可用
  if (typeof gsap !== 'undefined' && gsap.registerPlugin && typeof ScrollTrigger !== 'undefined') {
    gsap.registerPlugin(ScrollTrigger);
    
    // 为每个特性标签添加滚动动画
    featureTags.forEach((tag, index) => {
      gsap.fromTo(tag, 
        {
          opacity: 0,
          y: 20,
          scale: 0.9
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: tag,
            start: "top 80%",    // 当元素顶部到达视口80%位置时触发
            end: "bottom 20%",   // 当元素底部到达视口20%位置时结束
            toggleActions: "play none none reverse"  // 播放、暂停、恢复、反向
          }
        }
      );
    });
  }
}
```

## 🎛️ 动画参数详解

### 时长控制 (Duration)
```javascript
duration: 0.5,   // 快速动画
duration: 1.0,   // 标准动画
duration: 1.5,   // 慢速动画
duration: 2.0,   // 很慢动画
```

### 缓动函数 (Easing)
```javascript
ease: "none",           // 线性，无缓动
ease: "power1.out",     // 轻微缓出
ease: "power2.out",     // 标准缓出（推荐）
ease: "power3.out",     // 强烈缓出
ease: "back.out(1.7)",  // 回弹效果
ease: "elastic.out(1, 0.3)",  // 弹性效果
ease: "bounce.out",     // 弹跳效果
```

### 时间偏移 (Position Parameter)
```javascript
"<",      // 与前一个动画同时开始
"<0.5",   // 前一个动画开始后0.5秒开始
">",      // 前一个动画结束后开始
">0.2",   // 前一个动画结束后0.2秒开始
"+=0.3",  // 时间线当前位置后0.3秒开始
```

## 🔧 如何调整动画

### 1. 修改图片动画速度
在 `changeFeature` 函数中找到：
```javascript
tl.to(featureImages, {
  // ... 动画属性
  duration: 1.0,  // 👈 修改这里：0.5=快速, 1.5=慢速
  ease: "power2.out"
});
```

### 2. 修改文字动画速度
```javascript
tl.fromTo(featureTexts[currentIndex], {}, {
  // ... 动画属性
  duration: 1.0,  // 👈 修改这里
  ease: "power2.out"
});
```

### 3. 修改滚动动画
```javascript
scrollTrigger: {
  trigger: element,
  start: "top 80%",     // 👈 触发位置：top 90% = 更早触发
  end: "bottom 20%",    // 👈 结束位置
  toggleActions: "play none none reverse"  // 👈 动画行为
}
```

## 🎨 常用动画效果

### 淡入效果
```javascript
gsap.fromTo(element, 
  { opacity: 0 }, 
  { opacity: 1, duration: 1.0 }
);
```

### 滑入效果
```javascript
gsap.fromTo(element, 
  { x: -50, opacity: 0 }, 
  { x: 0, opacity: 1, duration: 1.0 }
);
```

### 缩放效果
```javascript
gsap.fromTo(element, 
  { scale: 0.8, opacity: 0 }, 
  { scale: 1, opacity: 1, duration: 1.0 }
);
```

### 旋转效果
```javascript
gsap.fromTo(element, 
  { rotation: -10, opacity: 0 }, 
  { rotation: 0, opacity: 1, duration: 1.0 }
);
```

## 🚀 最佳实践

### 1. 性能优化
- 使用 `will-change: transform` CSS 属性
- 避免动画 `width`、`height` 等会触发重排的属性
- 优先使用 `transform` 和 `opacity`

### 2. 用户体验
- 动画时长通常在 0.3-1.2 秒之间
- 避免过于复杂的缓动函数
- 提供禁用动画的选项（`prefers-reduced-motion`）

### 3. 调试技巧
- 使用浏览器开发者工具的 Performance 面板
- 添加 `console.log` 跟踪动画状态
- 使用 GSAP 的 `.progress()` 方法检查动画进度

## 📱 响应式动画

### 移动端优化
```javascript
// 检测设备类型
const isMobile = window.innerWidth < 768;

// 根据设备调整动画
const animationDuration = isMobile ? 0.6 : 1.0;
const animationEase = isMobile ? "power1.out" : "power2.out";
```

## 🔍 故障排除

### 常见问题

#### 1. 图片动画速度调整无效
**问题**: 修改了 `duration` 但图片动画速度没有变化

**原因**: 可能有多个地方控制图片动画，或者缓存问题

**解决方案**:
```javascript
// 确保修改正确的位置（第935-964行）
tl.to(featureImages, {
  // ... 所有动画属性
  duration: 1.5,  // 👈 确保这里被修改了
  ease: "power2.out",
  stagger: 0.0
});

// 清除浏览器缓存后测试
// 或者添加时间戳强制刷新
```

**检查步骤**:
1. 确认修改了正确的文件位置
2. 保存文件后硬刷新浏览器 (Ctrl+F5)
3. 检查开发者工具 Console 是否有错误
4. 确认 Shopify 开发服务器正在运行

#### 2. 动画不执行
- 检查 GSAP 是否正确加载
- 检查 JavaScript 控制台错误

#### 3. 动画卡顿
- 避免动画 `width`、`height` 等属性
- 使用 `transform` 和 `opacity`

#### 4. 时间不同步
- 确保使用相同的 timeline
- 检查时间偏移参数

### 调试代码
```javascript
// 检查 GSAP 状态
console.log('GSAP available:', typeof gsap !== 'undefined');
console.log('ScrollTrigger available:', typeof ScrollTrigger !== 'undefined');

// 监听动画事件
tl.eventCallback("onComplete", () => {
  console.log("Animation completed");
});

// 检查动画时长
console.log('Animation duration:', tl.duration());
```

## 📍 精确的代码位置

### 图片动画控制
**文件**: `sections/ir3-v2-key-features.liquid`
**行数**: 第 935-964 行
```javascript
tl.to(featureImages, {
  // ... 动画属性
  duration: 1.0,  // 👈 第961行：图片动画时长
  ease: "power2.out",  // 👈 第962行：图片动画缓动
  stagger: 0.0  // 👈 第963行：交错延迟
});
```

### 文字动画控制
**文件**: `sections/ir3-v2-key-features.liquid`
**行数**: 第 966-981 行
```javascript
tl.fromTo(featureTexts[currentIndex], {}, {
  // ... 动画属性
  duration: 1.0,  // 👈 文字动画时长
  ease: "power2.out"  // 👈 文字动画缓动
});
```

### 滚动动画初始化
**文件**: `sections/ir3-v2-key-features.liquid`
**行数**: 文件底部（约第1400行后）
```javascript
function initializeScrollAnimations() {
  // 滚动触发的动画设置
}
```

## 🎯 快速测试方法

### 1. 测试图片动画速度
修改第961行的 `duration: 1.0` 为：
- `duration: 0.5` - 快速测试
- `duration: 2.0` - 慢速测试

### 2. 测试文字动画速度
修改文字动画的 `duration: 1.0` 为不同值

### 3. 立即看到效果
1. 保存文件
2. 硬刷新浏览器 (Ctrl+F5 或 Cmd+Shift+R)
3. 点击 Next/Previous 按钮测试
