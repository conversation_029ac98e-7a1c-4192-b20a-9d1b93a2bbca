/* IR3 Tech Innovation Section - Premium Design with Key Features Font System */

.tech-innovation-section {
  position: relative;
  width: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(66, 165, 245, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(29, 233, 182, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(255, 149, 0, 0.04) 0%, transparent 60%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #0f0f0f 70%, #080808 100%);
  color: #ffffff;
  overflow: hidden;
  height: 100vh;
  max-height: 100vh;
  box-sizing: border-box;
}

/* 背景装饰层 */
.tech-innovation-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 1;
  opacity: 0.5;
}

/* 主容器 */
.tech-innovation-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  z-index: 10;
  height: 100vh;
  box-sizing: border-box;
}

/* 标题区域 - 使用Key Features字体系统 */
.section-header {
  text-align: center;
  position: absolute;
  top: 20px;
  left: 2rem;
  right: 2rem;
  height: 20vh;
  z-index: 10;
  overflow: visible;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.section-badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 20px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 25px;
  margin-bottom: 0.5rem;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.section-badge:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(66, 165, 245, 0.4);
  transform: translateY(-2px);
}

.section-title {
  font-family: 'Montserrat', sans-serif;
  font-size: clamp(32px, 4.5vw, 48px);
  font-weight: 900;
  letter-spacing: -0.5px;
  line-height: 1.4;
  margin-bottom: 12px;
  margin-top: 4rem;
  position: relative;
  display: inline-block;
  background: linear-gradient(135deg, #fff 0%, #42a5f5 50%, #1de9b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  font-family: 'Open Sans', sans-serif;
  font-size: 18px;
  font-weight: 300;
  line-height: 1.5;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  color: rgba(255, 255, 255, 0.9);
}

/* 对比容器 - 自由流动布局 */
.comparison-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1.5rem;
  align-items: center;
  position: absolute;
  top: calc(20vh + 30px);
  left: 2rem;
  right: 2rem;
  height: 60vh;
  overflow: hidden;
}

.comparison-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(66, 165, 245, 0.3) 20%, rgba(255, 255, 255, 0.5) 50%, rgba(29, 233, 182, 0.3) 80%, transparent 100%);
  z-index: 1;
  transform: translateY(-50%);
}

/* 技术侧边 */
.tech-side {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  z-index: 5;
}

.tech-visual {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 传送带图片容器 - 自由开放设计 */
.belt-image-container {
  position: relative;
  width: 100%;
  max-width: 350px;
  aspect-ratio: 1;
  border-radius: 20px;
  overflow: hidden;
  background: transparent;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.belt-image-container:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.4),
    0 12px 24px rgba(66, 165, 245, 0.2);
}

/* 交互式媒体容器样式 */
.interactive-media-container,
.interactive-media-container-left {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* 容器悬停时的微妙动画效果 */
.interactive-media-container:hover,
.interactive-media-container-left:hover {https://github.com/SoftFever/OrcaSlicer
  transform: scale(1.02);https://github.com/SoftFever/OrcaSlicerhttps://github.com/SoftFever/OrcaSlicer
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 静态图片和视频的基础样式 */
.static-image,
.interactive-video,
.static-image-left,
.interactive-video-left {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 默认状态：显示图片，隐藏视频 */
.static-image,
.static-image-left {
  opacity: 1;
  z-index: 2;
}

.interactive-video,
.interactive-video-left {
  opacity: 0;
  z-index: 1;
}

/* 悬停状态：通过JavaScript直接控制，不使用CSS :hover */
/* 过渡效果由JavaScript设置内联样式触发 */

/* 交互提示元素样式 - 精致毛玻璃效果 */
.interaction-hint {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  right: auto !important;
  bottom: auto !important;
  transform: translate(-50%, -50%) !important;
  display: flex;
  align-items: center;
  gap: 10px;
  /*精致毛玻璃背景*/background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  /*边框和阴影*/border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:    0 8px 32px rgba(0, 0, 0, 0.1),    0 2px 8px rgba(0, 0, 0, 0.05),    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  padding: 12px 18px;
  border-radius: 25px;
  color: white;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.3px;
  z-index: 10;
  /*初始隐藏状态*/opacity: 0;
  visibility: hidden;
  /*🕐渐入渐出速度控制：修改1.3s可调整提示显示/隐藏的过渡速度*/transition: all 1.3s cubic-bezier(0.4, 0, 0.2, 1);
  /*🔄定时显示动画：修改10s可调整整个循环时间（3秒等待+2秒显示+5秒等待）*/animation: timedAppearance 10s ease-in-out infinite;
  -webkit-transition: all 1.3s cubic-bezier(0.4, 0, 0.2, 1);
  -moz-transition: all 1.3s cubic-bezier(0.4, 0, 0.2, 1);
  -ms-transition: all 1.3s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 1.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interaction-hint:hover {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.05) !important;
  background: rgba(255, 255, 255, 0.25);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 🎮 鼠标悬停容器时快速隐藏提示动画 */
.interactive-media-container:hover .interaction-hint,
.interactive-media-container-left:hover .interaction-hint {
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translate(-50%, -50%) scale(0.8) !important;
  animation: none !important;
  /* ⚡ 快速隐藏过渡效果 - 0.12秒极速消失 */
  transition: all 0.12s cubic-bezier(0.4, 0, 1, 1) !important;
}

.play-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: white;
  /* 🎯 播放图标弹跳动画：修改2s可调整弹跳速度，修改infinite可控制循环次数 */
  animation: subtleBounce 2s ease-in-out infinite;
}

.play-icon svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.hint-text {
  white-space: nowrap;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 🎬 定时显示动画时间轴控制 */
@keyframes timedAppearance {
  0%, 30% {
    /* 📍 0-30%（0-3秒）：隐藏等待状态 - 修改30%可调整等待时间 */
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) scale(0.8) !important;
  }
  40% {
    /* 📍 40%（4秒）：渐入完成状态 - 修改40%可调整渐入结束时间 */
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1) !important;
  }
  60% {
    /* 📍 60%（6秒）：完全显示状态 - 修改60%可调整显示持续时间 */
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1) !important;
  }
  70%, 100% {
    /* 📍 70%-100%（7-10秒）：渐出过程+隐藏状态 - 修改70%可调整渐出开始时间 */
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) scale(0.8) !important;
  }
}

/* 🎯 播放图标弹跳动画 */
@keyframes subtleBounce {
  0%, 100% {
    /* 📍 修改translateY和scale值可调整弹跳幅度 */
    transform: translateY(0) scale(1);
  }
  50% {
    /* 📍 修改-2px可调整弹跳高度，修改1.1可调整缩放程度 */
    transform: translateY(-2px) scale(1.1);
  }
}

/* 呼吸光效动画 */
@keyframes breathingGlow {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.4),
      0 0 20px rgba(255, 255, 255, 0.1);
  }
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
  .interactive-media-container.touch-active .static-image,
  .interactive-media-container-left.touch-active .static-image-left {
    opacity: 0;
  }

  .interactive-media-container.touch-active .interactive-video,
  .interactive-media-container-left.touch-active .interactive-video-left {
    opacity: 1;
  }

  /* 移动端提示样式调整 */
  .interaction-hint {
    padding: 8px 12px !important;
    font-size: 11px !important;
    border-radius: 18px !important;
    gap: 6px !important;
    /* 保持居中位置 */
    top: 50% !important;
    left: 50% !important;
    right: auto !important;
    bottom: auto !important;
    transform: translate(-50%, -50%) !important;
    /* 确保在图片之上 */
    z-index: 15 !important;
  }

  .hint-text {
    display: none; /* 移动端只显示播放图标 */
  }

  .play-icon {
    width: 14px;
    height: 14px;
  }

  .play-icon svg {
    width: 14px;
    height: 14px;
  }

  /* 移动端角标位置调整 */
  .image-overlay {
    top: 0.75rem;
    right: 0.75rem;
    z-index: 12;
  }

  .issue-badge,
  .advantage-badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.6rem;
  }

  .interactive-media-container.touch-active .interactive-video,
  .interactive-media-container-left.touch-active .interactive-video-left {
    opacity: 1;
  }
}

/* 视频加载状态 */
.interactive-video:not([data-loaded="true"]) {
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* 确保视频在所有设备上正确显示 */
.interactive-video {
  pointer-events: none; /* 防止视频阻止鼠标事件 */
}

/* 性能优化：减少重绘 */
.interactive-media-container {
  will-change: transform;
}

.static-image,
.interactive-video {
  will-change: opacity;
}

/* 视频加载错误时的回退样式 */
.interactive-media-container.video-error .interactive-video {
  display: none !important;
}

.interactive-media-container.video-error .static-image {
  opacity: 1 !important;
}

/* 禁用减少动画偏好时的过渡效果 */
@media (prefers-reduced-motion: reduce) {
  .static-image,
  .interactive-video {
    transition: none !important;
  }
}

.belt-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  filter: brightness(1.1) contrast(1.05) saturate(1.1);
  background: transparent;
}

/* 图片覆盖层 */
.image-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 12; /* 确保角标在交互提示之上 */
}

/* 交互提示需要相对于容器定位，而不是image-overlay */
.belt-image-container .interaction-hint {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  right: auto !important;
  bottom: auto !important;
  transform: translate(-50%, -50%) !important;
  /* 重置父元素的影响 */
  margin: 0 !important;
}

.issue-badge,
.advantage-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.issue-badge {
  background: rgba(255, 59, 48, 0.1);
  color: #ff3b30;
  border: 1px solid rgba(255, 59, 48, 0.2);
}

.advantage-badge {
  background: rgba(52, 199, 89, 0.1);
  color: #34c759;
  border: 1px solid rgba(52, 199, 89, 0.2);
}

/* VS 分隔符 - 高级设计 */
.vs-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 10;
}

.vs-circle {
  position: relative;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background:
    conic-gradient(from 0deg, #42a5f5 0deg, #1de9b6 120deg, #ff9500 240deg, #42a5f5 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 15px 35px rgba(66, 165, 245, 0.4),
    0 5px 15px rgba(29, 233, 182, 0.3),
    inset 0 2px 4px rgba(255, 255, 255, 0.2);
  z-index: 10;
  transition: all 0.3s ease;
  animation: vsRotate 8s linear infinite, glow 3s ease-in-out infinite alternate;
  /* 添加独立的脉冲效果 */
  position: relative;
  cursor: pointer;
  /* 确保默认状态 */
  transform: scale(1) rotate(0deg);
  /* 禁用文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 🔄 VS按钮自动脉冲效果 - 使用伪元素避免动画冲突 */
.vs-circle::after {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background: conic-gradient(from 0deg,    rgba(66, 165, 245, 0.4) 0deg,    rgba(29, 233, 182, 0.4) 120deg,    rgba(255, 149, 0, 0.4) 240deg,    rgba(66, 165, 245, 0.4) 360deg);
  opacity: 0;
  z-index: -1;
  animation: pulseRing 5s ease-in-out infinite;
  pointer-events: none;
  -webkit-animation: pulseRing 5s ease-in-out infinite;
}

/* 🎯 VS按钮悬停效果 - 增强可点击感知 */
.vs-circle:hover {
  transform: scale(1.15);
  box-shadow:
    0 25px 60px rgba(66, 165, 245, 0.7),
    0 12px 30px rgba(29, 233, 182, 0.6),
    0 6px 15px rgba(255, 149, 0, 0.4),
    inset 0 3px 6px rgba(255, 255, 255, 0.4);
  animation-play-state: paused;
  /* 增强发光效果 */
  filter: drop-shadow(0 0 20px rgba(66, 165, 245, 0.8));
  /* 更明显的过渡效果 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* VS文本悬停时的额外效果 */
.vs-circle:hover .vs-text {
  transform: scale(1.05);
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

/* 悬停时的脉冲光环效果 */
.vs-circle:hover::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: conic-gradient(from 0deg,
    rgba(66, 165, 245, 0.3) 0deg,
    rgba(29, 233, 182, 0.3) 120deg,
    rgba(255, 149, 0, 0.3) 240deg,
    rgba(66, 165, 245, 0.3) 360deg);
  animation: hoverPulse 2s ease-in-out infinite;
  z-index: -1;
}

/* 悬停脉冲动画 */
@keyframes hoverPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* 🎯 VS按钮点击动画效果 - 连续点击优化版 */
.vs-circle.vs-clicked {
  /* 点击时暂停原有动画，只执行点击动画 */
  animation: vsClickSequence 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 增强点击反馈 */
  z-index: 15;
  /* 确保动画优先级和平滑重启 */
  animation-fill-mode: both;
  animation-iteration-count: 1;
}

.vs-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  /* 禁用文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  /* 平滑过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 技术信息 - 自由开放布局 */
.tech-info {
  text-align: center;
  padding: 1rem 0.5rem;
  background: transparent;
}

.tech-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #ffffff;
  letter-spacing: -0.3px;
}

.tech-subtitle {
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
  font-weight: 300;
}

.tech-specs {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0 0 0;
  text-align: left;
  max-width: 350px;
  margin-left: auto;
  margin-right: auto;
}

.tech-specs li {
  padding: 10px 0;
  font-family: 'Open Sans', sans-serif;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  padding-left: 32px;
  font-weight: 400;
  line-height: 1.5;
  transition: all 0.3s ease;
}

.tech-specs li:hover {
  color: rgba(255, 255, 255, 1);
  transform: translateX(4px);
}

.tech-specs li::before {
  content: '▸';
  position: absolute;
  left: 4px;
  top: 10px;
  color: #ff6b6b;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  line-height: 1.5;
}

.new-tech .tech-specs li::before {
  color: #1de9b6;
  content: '✓';
}

.tech-specs li:hover::before {
  transform: scale(1.2);
}

/* 技术数据展示 - 自由开放设计 */
.tech-data {
  text-align: center;
  position: absolute;
  bottom: -40px;  /* 进一步向下移动，确保不重叠 */
  left: 2rem;
  right: 2rem;
  height: calc(25vh - 20px);
  overflow: visible;
  z-index: 10;
}

/* 移除强制可见规则，让动画控制透明度 */

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

.data-item {
  position: relative;
  padding: 1rem 0.8rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  backdrop-filter: blur(10px);
  /* 移除强制可见性，让GSAP控制 */
  /* opacity: 1 !important;
  visibility: visible !important; */
}

.data-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #42a5f5 0%, #1de9b6 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.data-item:hover {
  transform: translateY(-8px) scale(1.05);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(66, 165, 245, 0.3);
  box-shadow:
    0 15px 30px rgba(66, 165, 245, 0.2),
    0 5px 15px rgba(0, 0, 0, 0.3);
}

.data-item:hover::before {
  transform: scaleX(1);
}

.data-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.5rem;
  font-weight: 900;
  /* 备用颜色，防止渐变失效时文字不可见 */
  color: #42a5f5;
  background: linear-gradient(135deg, #42a5f5 0%, #1de9b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  line-height: 0.9;
  letter-spacing: -1px;
  text-shadow: 0 0 20px rgba(66, 165, 245, 0.4);
  filter: drop-shadow(0 2px 4px rgba(66, 165, 245, 0.2));
  position: relative;
}

/* 为不支持background-clip的浏览器提供备用样式 */
@supports not (-webkit-background-clip: text) {
  .data-value {
    color: #42a5f5;
    -webkit-text-fill-color: initial;
  }
}



.data-label {
  font-family: 'Open Sans', sans-serif;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.85);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
  line-height: 1.4;
}

/* 动画关键帧 */
@keyframes vsRotate {
  0% {
    background: conic-gradient(from 0deg, #42a5f5 0deg, #1de9b6 120deg, #ff9500 240deg, #42a5f5 360deg);
  }
  33% {
    background: conic-gradient(from 120deg, #42a5f5 0deg, #1de9b6 120deg, #ff9500 240deg, #42a5f5 360deg);
  }
  66% {
    background: conic-gradient(from 240deg, #42a5f5 0deg, #1de9b6 120deg, #ff9500 240deg, #42a5f5 360deg);
  }
  100% {
    background: conic-gradient(from 360deg, #42a5f5 0deg, #1de9b6 120deg, #ff9500 240deg, #42a5f5 360deg);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% {
    box-shadow:
      0 15px 35px rgba(66, 165, 245, 0.4),
      0 5px 15px rgba(29, 233, 182, 0.3);
  }
  50% {
    box-shadow:
      0 20px 45px rgba(66, 165, 245, 0.6),
      0 8px 20px rgba(29, 233, 182, 0.5);
  }
}

/* 🔄 脉冲光环动画 - 提示用户可点击 */
@keyframes pulseRing {
  0%, 80% {
    /* 大部分时间保持隐藏 */
    opacity: 0;
    transform: scale(1);
  }
  85% {
    /* 开始出现 */
    opacity: 0.3;
    transform: scale(1.1);
  }
  90% {
    /* 最大脉冲 */
    opacity: 0.6;
    transform: scale(1.3);
  }
  95% {
    /* 开始消失 */
    opacity: 0.2;
    transform: scale(1.5);
  }
  100% {
    /* 完全消失 */
    opacity: 0;
    transform: scale(1.8);
  }
}

/* 🎯 VS按钮点击序列动画 - 分阶段优化版 */
@keyframes vsClickSequence {
  0% {
    transform: scale(1) rotate(0deg);
    box-shadow:
      0 15px 35px rgba(66, 165, 245, 0.4),
      0 5px 15px rgba(29, 233, 182, 0.3),
      inset 0 2px 4px rgba(255, 255, 255, 0.2);
    filter: drop-shadow(0 0 5px rgba(66, 165, 245, 0.3));
  }

  25% {
    /* 第一阶段：快速缩放 */
    transform: scale(1.15) rotate(90deg);
    box-shadow:
      0 20px 50px rgba(66, 165, 245, 0.6),
      0 10px 25px rgba(29, 233, 182, 0.4),
      inset 0 3px 6px rgba(255, 255, 255, 0.3);
    filter: drop-shadow(0 0 15px rgba(66, 165, 245, 0.6));
  }

  50% {
    /* 第二阶段：最大缩放 + 旋转 + 爆发效果 */
    transform: scale(1.3) rotate(180deg);
    box-shadow:
      0 35px 100px rgba(29, 233, 182, 1),
      0 20px 60px rgba(66, 165, 245, 0.8),
      0 10px 30px rgba(255, 149, 0, 0.6),
      0 0 50px rgba(255, 255, 255, 0.3),
      inset 0 4px 8px rgba(255, 255, 255, 0.5);
    filter: drop-shadow(0 0 35px rgba(29, 233, 182, 1));
  }

  75% {
    /* 第三阶段：继续旋转 + 发光 */
    transform: scale(1.2) rotate(270deg);
    box-shadow:
      0 25px 60px rgba(255, 149, 0, 0.7),
      0 12px 30px rgba(29, 233, 182, 0.5),
      0 6px 15px rgba(66, 165, 245, 0.4),
      inset 0 3px 6px rgba(255, 255, 255, 0.5);
    filter: drop-shadow(0 0 30px rgba(255, 149, 0, 0.7));
  }

  100% {
    /* 第四阶段：回到默认状态 */
    transform: scale(1) rotate(360deg);
    box-shadow:
      0 15px 35px rgba(66, 165, 245, 0.4),
      0 5px 15px rgba(29, 233, 182, 0.3),
      inset 0 2px 4px rgba(255, 255, 255, 0.2);
    filter: drop-shadow(0 0 5px rgba(66, 165, 245, 0.3));
  }
}

/* 💥 VS背景冲击效果样式 */
.vs-background-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vs-background-effects.active {
  opacity: 1;
  visibility: visible;
}

/* 背景颜色冲击层 */
.bg-color-impact {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center,
    rgba(255, 0, 100, 0.4) 0%,
    rgba(255, 50, 0, 0.3) 30%,
    rgba(0, 255, 255, 0.2) 60%,
    rgba(138, 43, 226, 0.1) 80%,
    transparent 100%);
  opacity: 0;
  z-index: -1;
}

.vs-background-effects.active .bg-color-impact {
  animation: colorImpact 1.2s ease-out;
}

/* 背景元素基础样式 */
.bg-element {
  position: absolute;
  opacity: 0;
  transform: scale(0);
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.vs-background-effects.active .bg-element {
  opacity: 1;
  transform: scale(1);
}



/* 闪电效果 */
.lightning {
  width: 4px;
  height: 100px;
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 1) 0%,
    rgba(0, 255, 255, 0.8) 50%,
    transparent 100%);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
  animation: lightningStrike 0.2s ease-in-out infinite alternate;
}

.lightning-1 {
  top: 20%;
  left: 25%;
  transform: rotate(15deg);
  transition-delay: 0.4s;
}

.lightning-2 {
  top: 30%;
  right: 30%;
  transform: rotate(-20deg);
  transition-delay: 0.5s;
}

.lightning-3 {
  bottom: 25%;
  left: 60%;
  transform: rotate(10deg);
  transition-delay: 0.6s;
}

/* 爆炸粒子 */
.explosion-particle {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 0, 100, 0.8) 50%,
    transparent 100%);
  border-radius: 50%;
  animation: explodeParticle 2s ease-out infinite;
}

.particle-1 { top: 40%; left: 20%; animation-delay: 0.1s; transition-delay: 0.7s; }
.particle-2 { top: 60%; right: 25%; animation-delay: 0.2s; transition-delay: 0.8s; }
.particle-3 { bottom: 40%; left: 30%; animation-delay: 0.3s; transition-delay: 0.9s; }
.particle-4 { top: 30%; right: 40%; animation-delay: 0.4s; transition-delay: 1s; }
.particle-5 { bottom: 30%; right: 20%; animation-delay: 0.5s; transition-delay: 1.1s; }
.particle-6 { top: 70%; left: 40%; animation-delay: 0.6s; transition-delay: 1.2s; }
.particle-7 { top: 25%; left: 60%; animation-delay: 0.7s; transition-delay: 1.3s; }
.particle-8 { bottom: 60%; right: 50%; animation-delay: 0.8s; transition-delay: 1.4s; }



/* 能量光束 */
.energy-beam {
  width: 2px;
  height: 150px;
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(0, 255, 255, 1) 50%,
    transparent 100%);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  animation: energyPulse 1s ease-in-out infinite alternate;
}

.beam-1 {
  top: 10%;
  left: 15%;
  transform: rotate(45deg);
  transition-delay: 1.5s;
}

.beam-2 {
  top: 15%;
  right: 20%;
  transform: rotate(-30deg);
  transition-delay: 1.6s;
}

.beam-3 {
  bottom: 20%;
  left: 25%;
  transform: rotate(-45deg);
  transition-delay: 1.7s;
}

.beam-4 {
  bottom: 15%;
  right: 30%;
  transform: rotate(60deg);
  transition-delay: 1.8s;
}

/* 冲击动画关键帧 */
@keyframes colorImpact {
  0% {
    opacity: 0;
    transform: scale(0.3);
    filter: blur(20px);
  }
  30% {
    opacity: 0.8;
    transform: scale(1.5);
    filter: blur(5px);
  }
  60% {
    opacity: 0.6;
    transform: scale(1.2);
    filter: blur(2px);
  }
  100% {
    opacity: 0.2;
    transform: scale(1);
    filter: blur(0px);
  }
}



@keyframes lightningStrike {
  0% { opacity: 1; transform: scaleY(1); }
  100% { opacity: 0.3; transform: scaleY(1.2); }
}

@keyframes explodeParticle {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  50% {
    transform: scale(1.5) translateY(-20px);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.5) translateY(-40px);
    opacity: 0;
  }
}



@keyframes energyPulse {
  0% {
    opacity: 0.5;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  }
  100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(0, 255, 255, 1);
  }
}

/* 响应式设计 */

/* 🖥️ 中等屏幕适配 (1200px-1600px) - 针对小屏幕笔记本 */
@media (max-width: 1600px) and (min-width: 1200px) {
  .tech-innovation-section {
    height: 100vh;
    min-height: 800px;
    padding: 2rem 0;
  }

  .tech-innovation-container {
    max-width: 90%;
    padding: 0 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .section-header {
    margin-bottom: 1.5rem;
    padding: 0 1rem;
    flex-shrink: 0;
  }

  .section-title {
    font-size: 32px;
    margin-bottom: 0.8rem;
    line-height: 1.2;
  }

  .section-description {
    font-size: 15px;
    line-height: 1.4;
    max-width: 600px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .comparison-container {
    gap: 2rem;
    margin-bottom: 1rem;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .belt-image-container {
    position: relative;
    width: 100%;
    max-width: 240px;
    height: 200px;
    aspect-ratio: unset;
    border-radius: 20px;
    overflow: hidden;
    background: transparent;
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  .belt-image {
    width: 100%;
    height: 100%;
    max-height: 180px;
    max-width: 220px;
    object-fit: contain;
    display: block;
  }

  .tech-info {
    padding: 0.8rem 0.5rem;
    max-width: 280px;
  }

  .tech-title {
    font-size: 16px;
    margin-bottom: 0.6rem;
  }

  .tech-subtitle {
    font-size: 12px;
    margin-bottom: 0.8rem;
  }

  .tech-specs {
    max-width: 260px;
    margin: 0.8rem auto 0;
  }

  .tech-specs li {
    font-size: 12px;
    padding: 4px 0;
    padding-left: 24px;
  }

  .tech-specs li::before {
    left: 2px;
    top: 4px;
    font-size: 12px;
  }

  .vs-circle {
    width: 45px;
    height: 45px;
    margin: 0.8rem 0;
  }

  .vs-text {
    font-size: 11px;
  }

  .tech-data {
    position: absolute;
    bottom: 4rem;
    left: 2rem;
    right: 2rem;
    height: auto;
    z-index: 10;
    flex-shrink: 0;
  }

  .data-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.8rem;
    max-width: 800px;
    margin: 0 auto;
  }

  .data-item {
    padding: 0.6rem;
    min-height: 60px;
  }

  .data-value {
    font-size: 1.2rem;
  }

  .data-label {
    font-size: 10px;
  }

  /* 交互提示缩小 */
  .interaction-hint {
    padding: 8px 12px !important;
    font-size: 11px !important;
    border-radius: 16px !important;
    gap: 6px !important;
  }

  .play-icon {
    width: 16px !important;
    height: 16px !important;
  }

  .hint-text {
    font-size: 11px !important;
  }
}

@media (max-width: 1024px) {
  .comparison-container {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .comparison-container::before {
    display: none;
  }

  .vs-divider {
    order: 2;
    margin: 2rem 0;
  }

  .old-tech {
    order: 1;
  }

  .new-tech {
    order: 3;
  }
}

/* 平板端优化 */
@media (max-width: 768px) {
  .tech-innovation-section {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    padding: 2rem 0 4rem 0;
    display: flex;
    flex-direction: column;
    overflow: visible;
  }

  .tech-innovation-container {
    padding: 0 1.5rem;
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    display: flex;
    flex-direction: column;
    gap: 3rem;
    flex: none;
    overflow: visible;
  }

  /* 标题区域 - 正常流布局 */
  .section-header {
    position: static;
    height: auto;
    margin-bottom: 2rem;
    text-align: center;
  }

  .section-title {
    font-size: clamp(28px, 6vw, 36px);
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .section-description {
    font-size: 16px;
    line-height: 1.6;
    padding: 0 1rem;
  }

  /* 对比容器 - 正常流布局 */
  .comparison-container {
    position: static;
    height: auto;
    display: flex;
    flex-direction: column;
    gap: 3rem;
    margin-bottom: 2.5rem;
    padding: 1rem 0;
    overflow: visible;
  }

  .comparison-container::before {
    display: none;
  }

  .tech-side {
    gap: 1.5rem;
  }

  .belt-image-container {
    max-width: 380px;
    margin: 0 auto;
    /* 确保层级关系正确 */
    position: relative;
    z-index: 1;
  }

  .tech-title {
    font-size: 20px;
    margin-bottom: 0.5rem;
  }

  .tech-subtitle {
    font-size: 14px;
    margin-bottom: 1rem;
  }

  .tech-specs {
    max-width: 300px;
  }

  .tech-specs li {
    font-size: 14px;
    padding: 8px 0;
    padding-left: 36px;
  }

  .tech-specs li::before {
    left: 6px;
    top: 8px;
  }

  .vs-circle {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .vs-text {
    font-size: 14px;
  }

  .vs-divider {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
  }

  /* 技术数据 - 正常流布局 */
  .tech-data {
    position: static;
    height: auto;
    margin-top: 3rem;
    margin-bottom: 2rem;
    padding: 2rem 0 3rem 0;
    overflow: visible;
    display: block;
    visibility: visible;
    z-index: 10;
  }

  .data-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
    max-width: 400px;
    margin: 0 auto;
  }

  .data-item {
    padding: 1.5rem 1rem;
    min-height: 100px;
  }

  .data-value {
    font-size: 2.2rem;
    margin-bottom: 0.8rem;
  }

  .data-label {
    font-size: 13px;
    line-height: 1.4;
  }
}

/* 手机端优化 */
@media (max-width: 480px) {
  .tech-innovation-section {
    padding: 2rem 0 4rem 0;
    min-height: auto !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible;
  }

  .tech-innovation-container {
    padding: 0 1rem;
    gap: 2.5rem;
    min-height: auto !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible;
  }

  .section-header {
    margin-bottom: 1.5rem;
  }

  .section-title {
    font-size: clamp(24px, 7vw, 32px);
    margin-top: 0.5rem;
  }

  .section-description {
    font-size: 15px;
    padding: 0 0.5rem;
  }

  .comparison-container {
    gap: 2.5rem;
    margin-bottom: 2rem;
    padding: 1rem 0;
  }

  .belt-image-container {
    max-width: 280px;
  }

  .tech-title {
    font-size: 18px;
  }

  .tech-subtitle {
    font-size: 13px;
  }

  .tech-specs {
    max-width: 280px;
  }

  .tech-specs li {
    font-size: 13px;
    padding: 6px 0;
    padding-left: 32px;
  }

  .tech-specs li::before {
    left: 4px;
    top: 6px;
    font-size: 14px;
  }

  .vs-circle {
    width: 55px;
    height: 55px;
  }

  .vs-text {
    font-size: 13px;
  }

  .tech-data {
    margin-top: -3rem;
    margin-bottom: 0rem;
    padding: 2rem 0 4rem 0;
    overflow: visible;
    display: block;
    visibility: visible;
    z-index: 10;
    background: transparent;
  }

  .data-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 320px;
    margin: 0 auto;
  }

  .data-item {
    padding: 1.5rem 1rem;
    min-height: 90px;
  }

  .data-value {
    font-size: 2rem;
    margin-bottom: 0.8rem;
  }

  .data-label {
    font-size: 12px;
    line-height: 1.4;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .tech-innovation-container {
    padding: 0 0.8rem;
  }

  .section-title {
    font-size: 22px;
  }

  .section-description {
    font-size: 14px;
  }

  .belt-image-container {
    max-width: 250px;
  }

  .tech-title {
    font-size: 16px;
  }

  .tech-specs li {
    font-size: 12px;
  }

  .data-value {
    font-size: 1.6rem;
  }

  .data-label {
    font-size: 10px;
  }
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
  /* 增加触摸目标大小 */
  .data-item {
    min-height: 80px;
    cursor: pointer;
  }

  .belt-image-container {
    cursor: pointer;
  }

  /* 优化移动端动画性能 */
  .belt-image-container,
  .vs-circle,
  .data-item {
    will-change: transform;
  }

  /* 移动端禁用某些动画以提升性能 */
  .old-tech .belt-image-container,
  .new-tech .belt-image-container {
    animation: none;
  }

  /* 移动端视频优化 */
  .interactive-video {
    transform: translateZ(0); /* 启用硬件加速 */
  }

  /* 移动端减少过渡时间 */
  .static-image,
  .interactive-video {
    transition-duration: 0.2s;
  }

  .vs-circle {
    animation: glow 3s ease-in-out infinite alternate;
  }

  /* 移动端间距微调 */
  .tech-specs {
    margin-top: 1rem;
  }

  .tech-info {
    padding: 0.8rem 0.3rem;
  }
}

/* 移动端横屏优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .tech-innovation-section {
    min-height: auto;
    padding: 1rem 0;
  }

  .tech-innovation-container {
    gap: 1.5rem;
  }

  .section-header {
    margin-bottom: 1rem;
  }

  .comparison-container {
    gap: 2rem;
  }

  .tech-data {
    margin-top: 1rem;
  }

  .data-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.8rem;
  }
}

/* 移动端高度修复 - 确保组件不会溢出 */
@media (max-width: 768px) {
  .tech-innovation-section {
    /* 强制覆盖桌面端的固定高度 */
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;

    /* 防止内容溢出到下一个组件 */
    contain: layout;
  }

  .tech-innovation-container {
    /* 强制覆盖桌面端的固定高度 */
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;

    /* 确保容器能完全包裹内容 */
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
}

/* 高分辨率移动设备优化 */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .belt-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 移动端无障碍优化 */
@media (max-width: 768px) {
  /* 确保焦点可见性 */
  .data-item:focus,
  .belt-image-container:focus {
    outline: 2px solid #42a5f5;
    outline-offset: 2px;
  }

  /* 减少动画对于偏好减少动画的用户 */
  @media (prefers-reduced-motion: reduce) {
    .belt-image-container,
    .vs-circle,
    .data-item {
      animation: none !important;
      transition: none !important;
    }
  }
}

/* 移动端性能优化 */
@media (max-width: 768px) {
  /* 使用GPU加速 */
  .belt-image-container,
  .vs-circle,
  .data-item {
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* 优化重绘性能 */
  .tech-innovation-section::before {
    opacity: 0.3;
    background-size: 60px 60px;
  }
}

/* 特殊效果增强 */
.old-tech .belt-image-container {
  animation: float 6s ease-in-out infinite;
}

.new-tech .belt-image-container {
  animation: float 6s ease-in-out infinite reverse;
}

.vs-circle {
  animation: vsRotate 8s linear infinite, glow 3s ease-in-out infinite alternate;
}

/* 悬停效果增强 */
.tech-side:hover .belt-image-container {
  animation-play-state: paused;
}

.data-item:nth-child(1) { animation-delay: 0.1s; }
.data-item:nth-child(2) { animation-delay: 0.2s; }
.data-item:nth-child(3) { animation-delay: 0.3s; }
.data-item:nth-child(4) { animation-delay: 0.4s; }
