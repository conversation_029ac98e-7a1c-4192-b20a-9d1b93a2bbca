/**
 * IR3 Hero Section Immersive Scroll Animation
 * File: assets/IR3-hero-section-1-immersive.js
 * 基于沉浸式滚动动画设计模式实现
 */

document.addEventListener('DOMContentLoaded', function() {
  // 等待GSAP加载完成
  if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
    console.error('IR3 Hero Immersive: GSAP or ScrollTrigger not loaded');
    return;
  }

  // 注册ScrollTrigger插件
  gsap.registerPlugin(ScrollTrigger);
  console.log('IR3 Hero Immersive: GSAP and ScrollTrigger loaded successfully');

  // 获取所有Hero Section
  const heroSections = document.querySelectorAll('.hero-section--immersive');
  
  heroSections.forEach(function(section) {
    initImmersiveHeroAnimation(section);
  });

  function initImmersiveHeroAnimation(section) {
    console.log('IR3 Hero Immersive: Initializing section', section.id);

    // 获取设置
    const settings = {
      scrollLock: section.dataset.scrollLock === 'true',
      scrollMultiplier: parseFloat(section.dataset.scrollMultiplier) || 1.5,
      animationSequence: section.dataset.animationSequence || 'sequential'
    };

    console.log('IR3 Hero Immersive: Settings', settings);

    // 获取容器和动画元素
    const heroContainer = section.querySelector('.hero-container');
    const animationElements = section.querySelectorAll('[data-animation-step]');
    
    if (!heroContainer) {
      console.error('IR3 Hero Immersive: Hero container not found');
      return;
    }

    console.log('IR3 Hero Immersive: Found', animationElements.length, 'animation elements');

    // 设置初始状态
    setupInitialStates(animationElements);

    // 创建ScrollTrigger配置
    const triggerConfig = {
      trigger: section,
      scrub: 1, // 平滑的滚动同步
      markers: false, // 生产环境关闭调试标记
      onUpdate: function(self) {
        updateAnimations(self.progress, animationElements, settings.animationSequence);
      }
    };

    // 根据是否启用滚动锁定调整配置
    if (settings.scrollLock) {
      const scrollDistance = window.innerHeight * settings.scrollMultiplier;
      triggerConfig.pin = heroContainer;
      triggerConfig.pinSpacing = true;
      triggerConfig.anticipatePin = 1;
      triggerConfig.start = 'top top';
      triggerConfig.end = `+=${scrollDistance}`;
      console.log('IR3 Hero Immersive: Using pinned mode with scroll distance:', scrollDistance);
    } else {
      triggerConfig.start = 'top bottom';
      triggerConfig.end = 'bottom top';
      console.log('IR3 Hero Immersive: Using normal scroll mode');
    }

    // 创建ScrollTrigger
    const scrollTrigger = ScrollTrigger.create(triggerConfig);
    console.log('IR3 Hero Immersive: ScrollTrigger created successfully');

    // 响应式处理
    ScrollTrigger.addEventListener('refresh', function() {
      console.log('IR3 Hero Immersive: ScrollTrigger refreshed');
    });
  }

  function setupInitialStates(elements) {
    console.log('IR3 Hero Immersive: Setting up initial states');
    
    elements.forEach(function(element) {
      // 设置初始隐藏状态
      gsap.set(element, {
        opacity: 0,
        y: 50,
        scale: 0.9,
        rotationX: 15,
        transformOrigin: 'center center',
        force3D: true
      });
    });
  }

  function updateAnimations(progress, elements, sequence) {
    console.log('IR3 Hero Immersive: Updating animations, progress:', progress);

    // 根据动画序列类型处理
    switch (sequence) {
      case 'sequential':
        updateSequentialAnimations(progress, elements);
        break;
      case 'simultaneous':
        updateSimultaneousAnimations(progress, elements);
        break;
      case 'staggered':
        updateStaggeredAnimations(progress, elements);
        break;
      default:
        updateSequentialAnimations(progress, elements);
    }
  }

  function updateSequentialAnimations(progress, elements) {
    const totalSteps = 7; // 总共7个动画步骤
    const stepProgress = progress * totalSteps;

    elements.forEach(function(element) {
      const step = parseInt(element.dataset.animationStep);
      const elementProgress = Math.max(0, Math.min(1, stepProgress - (step - 1)));
      
      if (elementProgress > 0) {
        animateElement(element, elementProgress, step);
      }
    });
  }

  function updateSimultaneousAnimations(progress, elements) {
    elements.forEach(function(element) {
      const step = parseInt(element.dataset.animationStep);
      animateElement(element, progress, step);
    });
  }

  function updateStaggeredAnimations(progress, elements) {
    const staggerDelay = 0.1; // 每个元素间隔0.1的进度
    
    elements.forEach(function(element) {
      const step = parseInt(element.dataset.animationStep);
      const delayedProgress = Math.max(0, Math.min(1, progress - (step - 1) * staggerDelay));
      
      if (delayedProgress > 0) {
        animateElement(element, delayedProgress, step);
      }
    });
  }

  function animateElement(element, progress, step) {
    // 使用缓动函数让动画更自然
    const easedProgress = easeOutCubic(progress);
    
    // 基础动画属性
    const opacity = easedProgress;
    const y = 50 * (1 - easedProgress);
    const scale = 0.9 + (0.1 * easedProgress);
    const rotationX = 15 * (1 - easedProgress);

    // 根据步骤添加特殊效果
    let additionalProps = {};
    
    switch (step) {
      case 1: // Pre-title
        additionalProps.x = -30 * (1 - easedProgress);
        break;
      case 2: // Main title
        additionalProps.scale = 0.8 + (0.2 * easedProgress);
        break;
      case 3: // Sub title
        additionalProps.x = 30 * (1 - easedProgress);
        break;
      case 4: // Tagline
        additionalProps.filter = `blur(${10 * (1 - easedProgress)}px)`;
        break;
      case 5: // Feature pills
        additionalProps.rotationY = 45 * (1 - easedProgress);
        break;
      case 6: // CTA buttons
        additionalProps.scale = 0.7 + (0.3 * easedProgress);
        break;
      case 7: // Product showcase
        additionalProps.rotationY = -30 * (1 - easedProgress);
        additionalProps.scale = 0.8 + (0.2 * easedProgress);
        break;
    }

    // 应用动画
    gsap.set(element, {
      opacity: opacity,
      y: y,
      scale: scale,
      rotationX: rotationX,
      force3D: true,
      ...additionalProps
    });
  }

  // 缓动函数
  function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
  }

  // 响应式处理
  let resizeTimeout;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
      ScrollTrigger.refresh();
      console.log('IR3 Hero Immersive: Refreshed after resize');
    }, 250);
  });

  console.log('IR3 Hero Immersive: Initialization complete');
});
