# API参考文档

## 概述

本文档提供Shopify Motion主题的API接口、配置选项和自定义方法的详细说明。

## 主题配置API

### 1. 全局配置对象

```javascript
window.theme = {
  config: {
    bpSmall: false,                    // 小屏断点状态
    bpSmallValue: 589,                 // 小屏断点值(px)
    hasSessionStorage: true,           // 会话存储支持
    mediaQuerySmall: 'screen and (max-width: 589px)',
    youTubeReady: false,              // YouTube API就绪状态
    vimeoReady: false,                // Vimeo API就绪状态
    vimeoLoading: false,              // Vimeo加载状态
    isTouch: boolean,                 // 触摸设备检测
    rtl: boolean                      // RTL语言支持
  },
  settings: {
    themeVersion: '11.0.0',           // 主题版本
    // 其他主题设置...
  }
};
```

### 2. 事件系统

#### 页面加载事件
```javascript
// 页面完全加载后触发
document.addEventListener('page:loaded', function() {
  // 自定义初始化代码
  console.log('页面已加载，主题资源就绪');
});

// 主题资源加载完成
document.addEventListener('theme:loaded', function() {
  // 主题特定的初始化
});
```

#### 动画事件
```javascript
// 动画开始事件
document.addEventListener('animation:start', function(e) {
  console.log('动画开始:', e.detail.element);
});

// 动画完成事件
document.addEventListener('animation:complete', function(e) {
  console.log('动画完成:', e.detail.element);
});
```

## Section配置API

### 1. IR3 Hero Section配置

#### Schema配置
```json
{
  "name": "IR3 Hero Section",
  "settings": [
    {
      "type": "text",
      "id": "main_title",
      "label": "主标题",
      "default": "Ideaformer IR3 V2"
    },
    {
      "type": "text",
      "id": "sub_title", 
      "label": "副标题",
      "default": "Professional Conveyor Belt 3D Printer"
    },
    {
      "type": "textarea",
      "id": "tagline",
      "label": "标语",
      "default": "Endless Printing Possibilities with Automatic Belt System"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "顶部边距",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 0,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "底部边距", 
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 0,
      "unit": "px"
    },
    {
      "type": "checkbox",
      "id": "enable_animations",
      "label": "启用动画效果",
      "default": true
    },
    {
      "type": "select",
      "id": "animation_speed",
      "label": "动画速度",
      "options": [
        {"value": "slow", "label": "慢速"},
        {"value": "normal", "label": "正常"},
        {"value": "fast", "label": "快速"}
      ],
      "default": "normal"
    }
  ]
}
```

#### Liquid模板使用
```liquid
<!-- 基础配置使用 -->
<h1>{{ section.settings.main_title | default: 'Ideaformer IR3 V2' }}</h1>
<h2>{{ section.settings.sub_title | default: 'Professional Conveyor Belt 3D Printer' }}</h2>

<!-- 条件渲染 -->
{% if section.settings.enable_animations %}
  <div class="animate-fade-in">动画内容</div>
{% endif %}

<!-- 动态样式 -->
<section style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;">
```

## JavaScript API

### 1. 动画控制器

#### AnimationController类
```javascript
class AnimationController {
  constructor(options = {}) {
    this.options = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px',
      ...options
    };
    this.observer = null;
    this.init();
  }

  // 初始化观察器
  init() {
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      this.options
    );
    this.observeElements();
  }

  // 处理元素进入视口
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.triggerAnimation(entry.target);
      }
    });
  }

  // 触发动画
  triggerAnimation(element) {
    const delay = element.dataset.delay || 0;
    
    setTimeout(() => {
      element.classList.add('is-visible');
      this.dispatchAnimationEvent('animation:start', element);
    }, delay * 1000);
  }

  // 观察指定元素
  observe(element) {
    if (this.observer) {
      this.observer.observe(element);
    }
  }

  // 停止观察元素
  unobserve(element) {
    if (this.observer) {
      this.observer.unobserve(element);
    }
  }

  // 销毁观察器
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
}

// 使用示例
const animationController = new AnimationController({
  threshold: 0.2,
  rootMargin: '0px 0px -100px 0px'
});
```

### 2. 磁性按钮API

```javascript
class MagneticButton {
  constructor(element, options = {}) {
    this.element = element;
    this.options = {
      strength: 0.1,
      duration: 0.3,
      ...options
    };
    this.init();
  }

  init() {
    this.element.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
  }

  handleMouseMove(e) {
    const rect = this.element.getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;
    
    const moveX = x * this.options.strength;
    const moveY = y * this.options.strength;
    
    this.element.style.transform = `translate(${moveX}px, ${moveY}px)`;
  }

  handleMouseLeave() {
    this.element.style.transform = 'translate(0px, 0px)';
  }

  // 更新配置
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions };
  }

  // 销毁实例
  destroy() {
    this.element.removeEventListener('mousemove', this.handleMouseMove);
    this.element.removeEventListener('mouseleave', this.handleMouseLeave);
  }
}

// 批量初始化
function initMagneticButtons() {
  const buttons = document.querySelectorAll('.magnetic-button');
  buttons.forEach(button => {
    new MagneticButton(button, {
      strength: button.dataset.strength || 0.1
    });
  });
}
```

### 3. 视差滚动API

```javascript
class ParallaxController {
  constructor() {
    this.elements = [];
    this.isScrolling = false;
    this.init();
  }

  init() {
    this.bindEvents();
    this.findElements();
  }

  bindEvents() {
    window.addEventListener('scroll', this.handleScroll.bind(this));
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  findElements() {
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    parallaxElements.forEach(element => {
      this.addElement(element);
    });
  }

  addElement(element) {
    const speed = parseFloat(element.dataset.parallax) || 0.5;
    const direction = element.dataset.parallaxDirection || 'vertical';
    
    this.elements.push({
      element,
      speed,
      direction,
      offset: element.getBoundingClientRect().top + window.pageYOffset
    });
  }

  handleScroll() {
    if (!this.isScrolling) {
      requestAnimationFrame(this.updateElements.bind(this));
      this.isScrolling = true;
    }
  }

  updateElements() {
    const scrollTop = window.pageYOffset;
    
    this.elements.forEach(item => {
      const { element, speed, direction, offset } = item;
      const yPos = (scrollTop - offset) * speed;
      
      if (direction === 'vertical') {
        element.style.transform = `translateY(${yPos}px)`;
      } else if (direction === 'horizontal') {
        element.style.transform = `translateX(${yPos}px)`;
      }
    });
    
    this.isScrolling = false;
  }

  handleResize() {
    // 重新计算元素偏移
    this.elements.forEach(item => {
      item.offset = item.element.getBoundingClientRect().top + window.pageYOffset;
    });
  }
}

// 全局实例
window.parallaxController = new ParallaxController();
```

## CSS自定义属性API

### 1. 颜色系统
```css
:root {
  /* 主色调 */
  --color-primary: #1c1d1d;
  --color-secondary: #ffffff;
  --color-accent: #00ffff;
  
  /* 状态颜色 */
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;
  
  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-tech: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
}
```

### 2. 动画时长
```css
:root {
  /* 动画时长 */
  --duration-fast: 0.2s;
  --duration-normal: 0.4s;
  --duration-slow: 0.8s;
  
  /* 缓动函数 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
```

### 3. 响应式断点
```css
:root {
  --bp-small: 589px;
  --bp-medium: 768px;
  --bp-large: 1024px;
  --bp-xlarge: 1200px;
}
```

## Liquid过滤器和标签

### 1. 自定义过滤器
```liquid
<!-- 动画延迟计算 -->
{{ forloop.index0 | times: 0.1 }}

<!-- 响应式图片 -->
{{ product.featured_image | img_url: '800x' | img_tag: loading: 'lazy' }}

<!-- 安全HTML输出 -->
{{ section.settings.description | strip_html | truncate: 150 }}
```

### 2. 条件渲染
```liquid
<!-- 动画开关 -->
{% if section.settings.enable_animations %}
  {% assign animation_class = 'animate-fade-in' %}
{% else %}
  {% assign animation_class = '' %}
{% endif %}

<!-- 设备检测 -->
{% if request.user_agent contains 'Mobile' %}
  <div class="mobile-only">移动端内容</div>
{% endif %}
```

## 错误处理和调试

### 1. 错误捕获
```javascript
// 全局错误处理
window.addEventListener('error', function(e) {
  console.error('主题错误:', e.error);
  
  // 发送错误报告（可选）
  if (theme.config.debug) {
    console.log('错误详情:', {
      message: e.message,
      filename: e.filename,
      lineno: e.lineno,
      colno: e.colno
    });
  }
});

// Promise错误处理
window.addEventListener('unhandledrejection', function(e) {
  console.error('未处理的Promise错误:', e.reason);
});
```

### 2. 调试工具
```javascript
// 调试模式检测
if (theme.config.debug || window.location.search.includes('debug=true')) {
  // 启用详细日志
  console.log('主题调试模式已启用');
  
  // 显示性能信息
  window.addEventListener('load', function() {
    console.log('页面加载时间:', performance.now() + 'ms');
  });
}
```

## 版本兼容性

### 支持的Shopify版本
- Shopify 2.0+
- Online Store 2.0 features

### 浏览器兼容性
- Chrome 60+
- Firefox 55+ 
- Safari 12+
- Edge 79+

### 降级策略
```javascript
// 功能检测和降级
if (!window.IntersectionObserver) {
  // 加载polyfill或使用替代方案
  loadPolyfill('intersection-observer');
}

if (!CSS.supports('transform', 'translateX(0)')) {
  // 禁用transform动画
  document.body.classList.add('no-transforms');
}
```

---

*API版本: v1.0 | 最后更新: 2025-07-07*
