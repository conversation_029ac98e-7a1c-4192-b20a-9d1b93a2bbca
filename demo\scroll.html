<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GSAP ScrollTrigger 锁定滚动动画示例</title>
    <style>
        /* --- 基础样式重置 --- */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #111;
            color: #fff;
            overflow-x: hidden; /* 防止横向滚动条 */
        }

        /* --- 引导提示 --- */
        .scroll-down-prompt {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 16px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }
        
        /* --- 面板通用样式 (每个全屏的 section) --- */
        .panel {
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden; /* 隐藏超出部分，确保动画元素从外部进入时不可见 */
        }

        .panel-content {
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        p {
            font-size: 1.5rem;
            max-width: 600px;
        }

        /* --- Section 1: 登场动画 --- */
        .section-1 {
            background-color: #2a2a2a;
        }
        .section-1 .box {
            width: 150px;
            height: 150px;
            background-color: #87ceeb; /* 天蓝色 */
            border-radius: 10px;
            /* 动画初始状态：完全透明，缩小 */
            opacity: 0;
            transform: scale(0.5);
        }

        /* --- Section 2: 探索动画 --- */
        .section-2 {
            background-color: #4a4e69;
        }
        .section-2 .circles-container {
            display: flex;
            gap: 30px;
        }
        .section-2 .circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: #fca311; /* 亮橙色 */
            /* 动画初始状态：在下方且透明 */
            transform: translateY(200px);
            opacity: 0;
        }

        /* --- Section 3: 汇聚动画 --- */
        .section-3 {
            background-color: #e63946; /* 鲜红色 */
        }
        .section-3 .line {
            position: absolute;
            height: 5px;
            background-color: #f1faee; /* 淡白色 */
        }
        .line-1 {
            width: 0;
            left: -100%; top: 50%;
        }
        .line-2 {
            width: 0;
            right: -100%; top: 50%;
        }
        .section-3 h1 {
            /* 动画初始状态：放大且透明 */
            transform: scale(3);
            opacity: 0;
        }

        /* --- 结尾部分 --- */
        .final-section {
            height: 50vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #1d3557;
        }

    </style>
</head>
<body>

    <div class="scroll-down-prompt">👇 请向下滚动</div>

    <!-- 第一个场景：淡入与放大 -->
    <section class="panel section-1">
        <div class="panel-content">
            <h1>第一幕：登场</h1>
            <div class="box"></div>
        </div>
    </section>

    <!-- 第二个场景：元素交错出现 -->
    <section class="panel section-2">
        <div class="panel-content">
            <h1>第二幕：探索</h1>
            <div class="circles-container">
                <div class="circle"></div>
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </section>

    <!-- 第三个场景：线条汇聚与文字显示 -->
    <section class="panel section-3">
        <div class="panel-content">
            <div class="line line-1"></div>
            <div class="line line-2"></div>
            <h1>第三幕：高潮</h1>
        </div>
    </section>
    
    <!-- 结尾部分，证明可以滚动下来 -->
    <section class="final-section">
        <h1>动画已结束</h1>
    </section>

    <!-- 引入 GSAP 核心库和 ScrollTrigger 插件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>

    <script>
        // 注册 ScrollTrigger 插件，这是使用它的第一步
        gsap.registerPlugin(ScrollTrigger);

        // --- 动画设置 ---

        // 1. 第一个 Section 的动画
        // 创建一个 GSAP 时间轴 (Timeline)，用于组织动画序列
        const tl1 = gsap.timeline({
            // 将这个时间轴与 ScrollTrigger 关联
            scrollTrigger: {
                trigger: ".section-1", // 触发动画的元素
                pin: true,             // 关键属性！将 .section-1 固定在屏幕上
                start: "top top",      // 当 .section-1 的顶部碰到视窗顶部时开始
                end: "+=2000",         // 动画在滚动 2000px 的距离内完成。因为被 pin 了，所以页面不会真的滚动，而是将这段滚动距离用来驱动动画。
                scrub: 1,              // 关键属性！使动画与滚动条平滑同步（数字越大越平滑，true为直接同步）
                // markers: true,      // 调试时可以开启，会显示触发器和滚动位置的标记
            }
        });

        // 在时间轴上添加入场动画
        tl1.to(".section-1 .box", {
            scale: 1,       // 放大到原大小
            opacity: 1,     // 完全显示
            rotation: 360,  // 顺便加个旋转
            duration: 1,    // 动画时长（在scrub模式下，这个值主要用来决定动画在总滚动距离中的占比）
        })
        .to(".section-1 h1", {
            y: -50,         // 标题向上移动
            opacity: 0,     // 标题消失
            duration: 0.5,
        }, "-=0.25"); // "-=0.25" 表示这个动画比上一个动画结束提前 0.25 秒开始

        
        // 2. 第二个 Section 的动画
        const tl2 = gsap.timeline({
            scrollTrigger: {
                trigger: ".section-2",
                pin: true,
                start: "top top",
                end: "+=2500",
                scrub: 1,
                // markers: true,
            }
        });

        // 使用 stagger 实现元素的交错动画，效果更生动
        tl2.to(".section-2 .circle", {
            y: 0,           // 移动到原始位置
            opacity: 1,     // 完全显示
            stagger: 0.3,   // 每个 .circle 动画之间延迟 0.3 秒
            duration: 1,
        })
        .to(".section-2 .circle", {
            rotation: -360, // 让它们转起来
            stagger: 0.2,
            duration: 1,
        }, "+=0.5"); // "+=0.5" 表示等上一个动画序列完成后，再等待 0.5 秒开始这个动画

        
        // 3. 第三个 Section 的动画
        const tl3 = gsap.timeline({
            scrollTrigger: {
                trigger: ".section-3",
                pin: true,
                start: "top top",
                end: "+=3000",
                scrub: 1,
                // markers: true,
            }
        });

        tl3.to(".line-1", {
            left: "50%",    // 从左侧向中心移动
            width: "50%",   // 宽度增长到 50%
            duration: 1
        })
        .to(".line-2", {
            right: "50%",   // 从右侧向中心移动
            width: "50%",   // 宽度增长到 50%
            duration: 1
        }, "<") // "<" 表示与上一个动画同时开始
        .to(".section-3 h1", {
            scale: 1,       // 恢复原大小
            opacity: 1,     // 完全显示
            duration: 1
        }, "-=0.5"); // 比线条动画结束提前 0.5 秒开始

    </script>
</body>
</html>