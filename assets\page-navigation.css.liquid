.page-navigation {
  position: fixed;
  {% if section.settings.position == 'right' %}
    right: 2rem;
  {% else %}
    left: 2rem;
  {% endif %}
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.page-nav-container {
  background: rgba(10, 10, 10, 0.85) !important;
  backdrop-filter: blur(15px);
  border-radius: 12px;
  padding: 1rem 0.75rem;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  min-width: 160px;
  max-width: 200px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.page-nav-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.3), transparent);
}

.page-nav-container:hover {
  transform: translateY(-1px);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(0, 255, 136, 0.2);
  border-color: rgba(0, 255, 136, 0.2);
}

.page-nav-header {
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.page-nav-title {
  font-size: 0.75rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-align: center;
}

.page-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.page-nav-item {
  margin-bottom: 0.5rem;
}

.page-nav-link {
  display: block;
  padding: 0.5rem 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  line-height: 1.3;
  letter-spacing: 0.02em;
  text-align: center;
}

.page-nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 2px;
  height: 0;
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  transform: translateY(-50%);
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 1px;
}

.page-nav-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 255, 136, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 6px;
}

.page-nav-link:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(0, 255, 136, 0.15);
}

.page-nav-link:hover::after {
  opacity: 1;
}

.page-nav-link:hover::before {
  height: 60%;
}

.page-nav-link.active {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  font-weight: 500;
  box-shadow:
    0 2px 8px rgba(0, 255, 136, 0.3),
    inset 0 1px 0 rgba(0, 255, 136, 0.2);
}

.page-nav-link.active::before {
  height: 80%;
}

.page-nav-link.active::after {
  opacity: 1;
}

/* Scroll Lock Tooltip */
.scroll-lock-tooltip {
  position: fixed;
  z-index: 1002;
  background: rgba(20, 20, 20, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.25rem;
  max-width: 300px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px) scale(0.9);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(0, 255, 136, 0.4);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(0, 255, 136, 0.15);
}

.scroll-lock-tooltip.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.tooltip-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
}

.tooltip-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
  filter: drop-shadow(0 0 4px rgba(255, 170, 0, 0.3));
}

.tooltip-text {
  flex: 1;
  min-width: 0; /* Allow text to wrap properly */
}

.tooltip-title {
  font-size: 1rem;
  font-weight: 700;
  color: #ffaa00;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.02em;
}

.tooltip-description {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-weight: 400;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.tooltip-arrow {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(20, 20, 20, 0.95);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

.tooltip-arrow.left {
  left: -4px;
  top: 50%;
  transform: translateY(-50%) rotate(135deg);
}

.tooltip-arrow.right {
  right: -4px;
  top: 50%;
  transform: translateY(-50%) rotate(-45deg);
}

.tooltip-arrow.bottom {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow.top {
  top: -4px;
  left: 50%;
  transform: translateX(-50%) rotate(-135deg);
}

.page-nav-progress {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-bar {
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1px;
  overflow: hidden;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #00ff88 0%, #0099ff 100%);
  border-radius: 1px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  width: var(--progress, 0%);
  box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.4), transparent);
  animation: shimmer 3s infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.page-nav-container:hover .progress-bar::before {
  opacity: 1;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .page-navigation {
    {% if section.settings.position == 'right' %}
      right: 1.5rem;
    {% else %}
      left: 1.5rem;
    {% endif %}
  }
}

@media (max-width: 1024px) {
  .page-navigation {
    {% if section.settings.position == 'right' %}
      right: 1rem;
    {% else %}
      left: 1rem;
    {% endif %}
  }

  .page-nav-container {
    min-width: 180px;
    max-width: 200px;
    padding: 1.25rem 0.875rem;
    border-radius: 16px;
  }

  .page-nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.8125rem;
  }
}

/* Mobile trigger button */
.mobile-nav-trigger {
  display: none !important;
  position: fixed !important;
  left: 1rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 48px !important;
  height: 48px !important;
  background: rgba(10, 10, 10, 0.9) !important;
  backdrop-filter: blur(15px) !important;
  border-radius: 50% !important;
  border: 2px solid rgba(0, 255, 136, 0.6) !important;
  cursor: pointer !important;
  z-index: 1001 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

/* Hide trigger button when mobile nav is active */
.mobile-nav-trigger.nav-open {
  opacity: 0;
  pointer-events: none;
  transform: translateY(-50%) scale(0.8);
}

.mobile-nav-trigger:hover {
  transform: translateY(-50%) scale(1.05);
  border-color: rgba(0, 255, 136, 0.5);
  box-shadow: 0 4px 20px rgba(0, 255, 136, 0.2);
}

.trigger-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: conic-gradient(from 0deg, rgba(0, 255, 136, 0.3) 0%, rgba(0, 255, 136, 0.3) var(--progress, 0%), transparent var(--progress, 0%));
  border-radius: 50%;
  transition: all 0.3s ease;
}

.trigger-icon {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 3px !important;
  z-index: 1 !important;
}

.trigger-dot {
  width: 4px !important;
  height: 4px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
}

.mobile-nav-trigger:hover .trigger-dot {
  background: #00ff88;
}

/* Mobile overlay */
.mobile-nav-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-nav-overlay.active {
  opacity: 1;
}

/* Mobile close button */
.mobile-nav-close {
  display: none;
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.6);
  font-size: 18px;
  line-height: 1;
  transition: color 0.3s ease;
}

.mobile-nav-close:hover {
  color: #00ff88;
}

.close-icon {
  display: block;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.mobile-nav-close:hover .close-icon {
  transform: rotate(90deg);
}

@media (max-width: 768px) {
  .page-navigation {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    transform: none;
    width: 100%;
    max-width: none;
    z-index: 1001;
    pointer-events: none;
  }

  .mobile-nav-trigger {
    display: block !important;
    pointer-events: all !important;
    opacity: 1 !important;
    visibility: visible !important;
  }

  .page-nav-container {
    position: fixed;
    left: -280px;
    top: 50%;
    transform: translateY(-50%);
    width: 260px;
    height: auto;
    max-height: 80vh;
    padding: 1.5rem 1rem;
    border-radius: 0 16px 16px 0;
    background: rgba(10, 10, 10, 0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-left: none;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    pointer-events: all;
    opacity: 0;
    z-index: 1002;
  }

  .page-nav-container.active {
    left: 0;
    opacity: 1;
  }

  .mobile-nav-close {
    display: block;
  }

  .page-nav-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 255, 136, 0.2);
    position: relative;
  }

  .page-nav-title {
    font-size: 0.875rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
  }

  .page-nav-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .page-nav-item {
    margin-bottom: 0;
  }

  .page-nav-link {
    text-align: left;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    background: transparent;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    line-height: 1.4;
    letter-spacing: 0.02em;
  }

  .page-nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 2px;
    height: 0;
    background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
    transform: translateY(-50%);
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 1px;
  }

  .page-nav-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 255, 136, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 6px;
  }

  .page-nav-link:hover {
    color: rgba(255, 255, 255, 0.95);
    background: rgba(0, 255, 136, 0.1);
    transform: translateX(4px);
  }

  .page-nav-link:hover::after {
    opacity: 1;
  }

  .page-nav-link:hover::before {
    height: 60%;
  }

  .page-nav-link.active {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    font-weight: 500;
    transform: translateX(4px);
    box-shadow:
      0 2px 8px rgba(0, 255, 136, 0.3),
      inset 0 1px 0 rgba(0, 255, 136, 0.2);
  }

  .page-nav-link.active::before {
    height: 80%;
  }

  .page-nav-link.active::after {
    opacity: 1;
  }

  .mobile-nav-overlay {
    display: block;
  }

  .mobile-nav-overlay.active {
    pointer-events: all;
  }

  /* Mobile-specific tooltip styles */
  .scroll-lock-tooltip {
    max-width: calc(100vw - 3rem) !important;
    font-size: 0.875rem !important;
    padding: 1.5rem !important;
    border-radius: 20px !important;
    backdrop-filter: blur(25px) !important;
    background: rgba(15, 15, 15, 0.98) !important;
    border: 1.5px solid rgba(0, 255, 136, 0.5) !important;
    box-shadow:
      0 25px 80px rgba(0, 0, 0, 0.8),
      0 10px 40px rgba(0, 0, 0, 0.6),
      0 4px 16px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(0, 255, 136, 0.2) !important;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
  }

  /* Remove transform overrides to allow JavaScript positioning */
  .scroll-lock-tooltip:not(.show) {
    transform: translateY(20px) scale(0.85);
  }

  .scroll-lock-tooltip.show {
    transform: scale(1);
  }

  .tooltip-content {
    gap: 1.25rem !important;
    align-items: center !important;
  }

  .tooltip-icon {
    font-size: 2rem !important;
    margin-top: 0 !important;
    filter: drop-shadow(0 0 6px rgba(255, 170, 0, 0.4)) !important;
  }

  .tooltip-title {
    font-size: 1.25rem !important;
    font-weight: 700 !important;
    margin-bottom: 0.75rem !important;
    color: #ffaa00 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
    letter-spacing: 0.03em !important;
  }

  .tooltip-description {
    font-size: 1rem !important;
    line-height: 1.6 !important;
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 400 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  }

  .tooltip-arrow {
    width: 12px !important;
    height: 12px !important;
    background: rgba(15, 15, 15, 0.98) !important;
    border: 1.5px solid rgba(0, 255, 136, 0.5) !important;
  }
}



/* Smaller mobile devices - keep drawer style but adjust sizing */
@media (max-width: 640px) {
  .page-nav-container {
    width: 240px;
    padding: 1.25rem 0.875rem;
  }

  .page-nav-link {
    padding: 0.625rem 0.875rem;
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .page-nav-container {
    width: 220px;
    padding: 1rem 0.75rem;
  }

  .page-nav-link {
    padding: 0.625rem 0.75rem;
    font-size: 0.8125rem;
    line-height: 1.3;
  }
}

@media (max-width: 360px) {
  .page-nav-container {
    width: 200px;
  }

  .page-nav-link {
    padding: 0.625rem;
    font-size: 0.8125rem;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .page-nav-container,
  .page-nav-link,
  .page-nav-link::before,
  .page-nav-link::after,
  .progress-bar::after {
    transition: none;
  }

  .page-nav-container:hover {
    transform: none;
  }

  .page-nav-link:hover,
  .page-nav-link.active {
    transform: none;
  }

  @keyframes shimmer {
    0%, 100% { transform: translateX(0); }
  }
}
