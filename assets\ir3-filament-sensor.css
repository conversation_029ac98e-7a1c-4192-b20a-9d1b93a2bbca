/* IR3 Filament Sensor Section Styles */
/* File: assets/ir3-filament-sensor.css */

/* Section Base Styles */
.filament-sensor-section {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

/* Background Video/Canvas Layer */
.sensor-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Canvas容器 - 模仿ir3-v2-auto-leveling-frames结构 */
.sensor-canvas-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.8s ease-in;
}

.sensor-canvas-container.loaded {
  opacity: 1;
}

.sensor-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: cover;
  will-change: transform;
  backface-visibility: hidden;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
  z-index: 3;
  pointer-events: none;
}

/* Content Container */
.sensor-content-container {
  position: relative;
  z-index: 4;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5% 0 65%; /* 左边距20%，右边距5% - 这里可以调整左右偏移 */
}

/* Text Content */
.text-content {
  color: #fff;
  max-width: 800px;
  width: 100%;
  text-align: center;
  position: relative;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Text Sections for Dynamic Switching */
.text-section {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateY(30px);
  width: 100%;
  opacity: 0;
  transition: opacity 0.8s ease, transform 0.8s ease;
  text-align: center;
}

.text-section.active {
  opacity: 1;
  transform: translate(-50%, -50%) translateY(0);
}

/* Filament Sensor Section Title - 使用特定的类名避免冲突 */
.filament-sensor-section .section-title {
  font-family: 'Montserrat', sans-serif;
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #fff 0%, #42a5f5 50%, #1de9b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(66, 165, 245, 0.3);
}

/* Filament Sensor Section Description - 使用特定的类名避免冲突 */
.filament-sensor-section .section-description {
  font-family: 'Open Sans', sans-serif;
  font-size: clamp(1.2rem, 2vw, 1.5rem);
  font-weight: 300;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}



/* Responsive Design */
@media (max-width: 768px) {
  /* 移动端Canvas容器 - 全屏显示，隐藏溢出 */
  .sensor-canvas-container {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
  }

  /* 移动端Canvas - 由JavaScript动态控制尺寸，居中显示 */
  .sensor-canvas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* JavaScript控制具体尺寸 */
  }

  /* 移动端背景容器 */
  .sensor-background {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  .sensor-content-container {
    padding: 0 5%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .text-content {
    max-width: 100%;
    min-height: auto;
    text-align: center;
    padding: 20px;
  }

  .section-title {
    font-size: 1.8rem;
    margin-bottom: 0.8rem;
    line-height: 1.2;
  }

  .section-description {
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.8rem;
  }

  .section-description {
    font-size: 0.9rem;
    line-height: 1.5;
  }
}

/* Loading States - Remove duplicate canvas styles */

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .sensor-canvas {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .section-title {
    background: #fff;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
