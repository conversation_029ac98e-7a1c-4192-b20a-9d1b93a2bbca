

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-575067858103436517.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-575067858103436517.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-575067858103436517.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-575067858103436517.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-575067858103436517.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-575067858103436517.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-575067858103436517.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-575067858103436517.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-575067858103436517.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-575067858103436517.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-575067858103436517.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-575067858103436517.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-575067858103436517.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-575067858103436517.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-575067858103436517.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-575067858103436517.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-575067858103436517.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-575067858103436517.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-575067858103436517.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-575067858103436517.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-575067858103436517.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-575067858103436517.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-575067858103436517.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-575067858103436517.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-575067858103436517.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-575067858103436517.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-575067858103436517.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-575067858103436517.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-575067858103436517.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-575067858103436517.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-575067858103436517.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-575067858103436517.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-575067858103436517.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-575067858103436517.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-575067858103436517.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-575067858103436517.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-575067858103436517.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-575067858103436517.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-575067858103436517.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-575067858103436517.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-575067858103436517.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-575067858103436517.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-575067858103436517.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-575067858103436517.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-575067858103436517.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-575067858103436517.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-575067858103436517.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-575067858103436517.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-575067858103436517.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-575067858103436517.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-575067858103436517.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-575067858103436517.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-575067858103436517.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-575067858103436517.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-575067858103436517.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-575067858103436517.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-575067858103436517.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-575067858103436517.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-575067858103436517.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-575067858103436517.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-575067858103436517.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-575067858103436517.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-575067858103436517.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-575067858103436517.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-575067858103436517.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-575067858103436517.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-575067858103436517.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-575067858103436517.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-575067858103436517.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-575067858103436517 .-gp-translate-x-1\/2,.gps-575067858103436517 .-gp-translate-y-1\/2,.gps-575067858103436517 .gp-rotate-0,.gps-575067858103436517 .gp-rotate-180,.gps-575067858103436517 .mobile\:gp-rotate-0,.gps-575067858103436517 .mobile\:gp-rotate-180,.gps-575067858103436517 .tablet\:gp-rotate-0,.gps-575067858103436517 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-575067858103436517 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-575067858103436517 .gp-visible{visibility:visible}.gps-575067858103436517 .gp-invisible{visibility:hidden}.gps-575067858103436517 .gp-static{position:static}.gps-575067858103436517 .gp-absolute{position:absolute}.gps-575067858103436517 .\!gp-relative{position:relative!important}.gps-575067858103436517 .gp-relative{position:relative}.gps-575067858103436517 .gp-left-0{left:0}.gps-575067858103436517 .gp-left-1\/2{left:50%}.gps-575067858103436517 .gp-right-0{right:0}.gps-575067858103436517 .gp-top-1\/2{top:50%}.gps-575067858103436517 .gp-z-1{z-index:1}.gps-575067858103436517 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-575067858103436517 .gp-my-0{margin-bottom:0;margin-top:0}.gps-575067858103436517 .gp-mb-0{margin-bottom:0}.gps-575067858103436517 .gp-block{display:block}.gps-575067858103436517 .\!gp-flex{display:flex!important}.gps-575067858103436517 .gp-flex{display:flex}.gps-575067858103436517 .gp-grid{display:grid}.gps-575067858103436517 .\!gp-hidden{display:none!important}.gps-575067858103436517 .gp-hidden{display:none}.gps-575067858103436517 .gp-aspect-square{aspect-ratio:1/1}.gps-575067858103436517 .gp-h-full{height:100%}.gps-575067858103436517 .\!gp-min-h-full{min-height:100%!important}.gps-575067858103436517 .gp-w-\[12px\]{width:12px}.gps-575067858103436517 .gp-w-\[68px\]{width:68px}.gps-575067858103436517 .gp-w-full{width:100%}.gps-575067858103436517 .\!gp-max-w-none{max-width:none!important}.gps-575067858103436517 .gp-max-w-full{max-width:100%}.gps-575067858103436517 .gp-shrink-0{flex-shrink:0}.gps-575067858103436517 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-575067858103436517 .-gp-translate-x-1\/2,.gps-575067858103436517 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575067858103436517 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-575067858103436517 .gp-rotate-0{--tw-rotate:0deg}.gps-575067858103436517 .gp-rotate-0,.gps-575067858103436517 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575067858103436517 .gp-rotate-180{--tw-rotate:180deg}.gps-575067858103436517 .gp-cursor-pointer{cursor:pointer}.gps-575067858103436517 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-575067858103436517 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-575067858103436517 .\!gp-flex-row{flex-direction:row!important}.gps-575067858103436517 .gp-flex-row{flex-direction:row}.gps-575067858103436517 .gp-flex-col{flex-direction:column}.gps-575067858103436517 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-575067858103436517 .gp-items-center{align-items:center}.gps-575067858103436517 .gp-justify-center{justify-content:center}.gps-575067858103436517 .gp-justify-between{justify-content:space-between}.gps-575067858103436517 .gp-gap-2{gap:8px}.gps-575067858103436517 .gp-gap-y-0{row-gap:0}.gps-575067858103436517 .gp-overflow-hidden{overflow:hidden}.gps-575067858103436517 .gp-rounded-full{border-radius:9999px}.gps-575067858103436517 .gp-bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0/var(--tw-bg-opacity))}.gps-575067858103436517 .gp-bg-cover{background-size:cover}.gps-575067858103436517 .gp-bg-center{background-position:50%}.gps-575067858103436517 .gp-text-center{text-align:center}.gps-575067858103436517 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-575067858103436517 .gp-opacity-100{opacity:1}.gps-575067858103436517 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-575067858103436517 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-575067858103436517 .gp-duration-200{transition-duration:.2s}.gps-575067858103436517 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-575067858103436517 .tablet\:gp-static{position:static}.gps-575067858103436517 .tablet\:\!gp-relative{position:relative!important}.gps-575067858103436517 .tablet\:gp-left-0{left:0}.gps-575067858103436517 .tablet\:gp-right-0{right:0}.gps-575067858103436517 .tablet\:gp-block{display:block}.gps-575067858103436517 .tablet\:\!gp-flex{display:flex!important}.gps-575067858103436517 .tablet\:\!gp-hidden{display:none!important}.gps-575067858103436517 .tablet\:gp-hidden{display:none}.gps-575067858103436517 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-575067858103436517 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-575067858103436517 .tablet\:gp-rotate-0,.gps-575067858103436517 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575067858103436517 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-575067858103436517 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-575067858103436517 .tablet\:gp-flex-row{flex-direction:row}.gps-575067858103436517 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-575067858103436517 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-575067858103436517 .mobile\:gp-static{position:static}.gps-575067858103436517 .mobile\:gp-left-0{left:0}.gps-575067858103436517 .mobile\:gp-right-0{right:0}.gps-575067858103436517 .mobile\:gp-block{display:block}.gps-575067858103436517 .mobile\:\!gp-hidden{display:none!important}.gps-575067858103436517 .mobile\:gp-hidden{display:none}.gps-575067858103436517 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-575067858103436517 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-575067858103436517 .mobile\:gp-rotate-0,.gps-575067858103436517 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-575067858103436517 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-575067858103436517 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-575067858103436517 .mobile\:gp-flex-row{flex-direction:row}.gps-575067858103436517 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-575067858103436517 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-575067858103436517 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-575067858103436517 .\[\&\>svg\]\:gp-w-full>svg{width:100%}.gps-575067858103436517 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-575067858103436517 .\[\&_\>_article\]\:gp-aspect-\[var\(--aspect\)\]>article{aspect-ratio:var(--aspect)}@media (max-width:1024px){.gps-575067858103436517 .tablet\:\[\&_\>_article\]\:gp-aspect-\[var\(--aspect-tablet\2c _var\(--aspect\)\)\]>article{aspect-ratio:var(--aspect-tablet,var(--aspect))}}@media (max-width:767px){.gps-575067858103436517 .mobile\:\[\&_\>_article\]\:gp-aspect-\[var\(--aspect-mobile\2c _var\(--aspect-tablet\2c _var\(--aspect\)\)\)\]>article{aspect-ratio:var(--aspect-mobile,var(--aspect-tablet,var(--aspect)))}}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gIYz5tnRQe" data-id="gIYz5tnRQe"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:60px;--pb:100px;--pr:60px;--pt-mobile:70px;--pl-mobile:15px;--pb-mobile:56px;--pr-mobile:0px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gIYz5tnRQe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2wTUNvdKc gp-relative gp-flex gp-flex-col"
    >
      
    <gp-carousel data-id="gjS6pgUWNb"  id="gp-root-carousel-gjS6pgUWNb-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"gjS6pgUWNb-{{section.id}}","setting":{"animationMode":"ease-in","arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"arrowButtonSize":{"desktop":{"height":"32px","padding":{"linked":true},"shapeLinked":true,"shapeValue":"1/1","width":"32px"}},"arrowCustom":"<svg height=\"20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\"  viewBox=\"0 0 256 256\" fill=\"currentColor\" data-id=\"508817723121664360\">\n              <path fill=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" fill=\"currentColor\" d=\"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm29.66,109.66-40,40a8,8,0,0,1-11.32-11.32L140.69,128,106.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,157.66,133.66Z\" /></svg>","arrowCustomColor":"#ECECEC","arrowGapToEachSide":"16","arrowIconSize":{"desktop":60},"autoplay":false,"autoplayTimeout":2,"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"childItem":["Slide 1","Slide 2","Slide 3","Slide 4","Slide 5"],"controlOverContent":{"desktop":true},"dot":{"desktop":true,"mobile":true,"tablet":true},"dotActiveColor":{"desktop":"line-3"},"dotColor":{"desktop":"bg-1"},"dotGapToCarousel":{"desktop":16},"dotSize":{"desktop":12},"dotStyle":{"desktop":"none"},"enableDrag":{"desktop":true},"itemNumber":{"desktop":5,"mobile":2,"tablet":4},"label":true,"loop":{"desktop":true},"navigationStyle":{"desktop":"outside","mobile":"none"},"pauseOnHover":true,"roundedArrow":{"desktop":{"radiusType":"small"}},"rtl":false,"runPreview":false,"showWhenHover":false,"sneakPeak":{"desktop":true},"sneakPeakOffsetCenter":{"desktop":50},"sneakPeakOffsetForward":{"desktop":10},"sneakPeakType":{"desktop":"forward"},"vertical":{"desktop":false}},"styles":{"align":{"desktop":"center"},"borderContent":{"border":"none","borderType":"none","borderWidth":"1px","color":"#121212","isCustom":true,"position":"all","width":"1px 1px 1px 1px"},"carouselShadow":{"angle":90,"blur":"12px","color":"#121212","distance":"4px","spread":"0px","type":"shadow-1"},"fullWidth":{"desktop":false,"mobile":false,"tablet":false},"hasActiveShadow":false,"playSpeed":500,"roundedContent":{"radiusType":"none"},"sizeSetting":{"desktop":{"gap":"","height":"auto","shapeLinked":false,"width":"default"}},"spacing":{"desktop":"12","mobile":"24"}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gjS6pgUWNb"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--w:var(--g-ct-w);--w-tablet:var(--g-ct-w);--w-mobile:var(--g-ct-w);--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous gjS6pgUWNb-{{section.id}} gp-carousel-arrow-gjS6pgUWNb gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-relative  tablet:!gp-flex tablet:!gp-relative  mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#ECECEC;--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px"
  >
    <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817723121664360">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm29.66,109.66-40,40a8,8,0,0,1-11.32-11.32L140.69,128,106.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,157.66,133.66Z" /></svg>
    </div>
      <style>
    .gp-carousel-arrow-gjS6pgUWNb {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gjS6pgUWNb::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gjS6pgUWNb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gjS6pgUWNb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gjS6pgUWNb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gjS6pgUWNb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gjS6pgUWNb-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:24px;--cg-tablet:12px;--cg:12px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gjS6pgUWNb g28wL3kqk8"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gsA4PxdYFT"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gsA4PxdYFT"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/sE8HkaP0fEU?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gsA4PxdYFT"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-f0940b64-02ca-463a-bf46-0542a17c5397.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gsA4PxdYFT">
        .gsA4PxdYFT[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gjS6pgUWNb gcQ20wQeiF"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gtv-NMfL7w"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gtv-NMfL7w"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/cyzh48XRS4M?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gtv-NMfL7w"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-a79845d6-ea84-4564-956a-d5e0ec829d24.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gtv-NMfL7w">
        .gtv-NMfL7w[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gjS6pgUWNb gSpOGuXw8o"
      data-index="2"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="g_FbyeqSub"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative g_FbyeqSub"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/cyzh48XRS4M?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-g_FbyeqSub"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-b722a3dc-8683-4d63-9fdb-ea1d1ea433e5.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-g_FbyeqSub">
        .g_FbyeqSub[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gjS6pgUWNb gy93IbDUps"
      data-index="3"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gQQMI1Vu5Q"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gQQMI1Vu5Q"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/E_v_Yvi9jKc?autoplay=1&state=1&mute=1&&controls=1"}'>
      
          <link rel="preconnect" href="https://www.youtube-nocookie.com" />
          <link rel="preconnect" href="https://www.google.com" />
        
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article "
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gQQMI1Vu5Q"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-30f58ab5-39ee-49e1-bd49-4e21d37545f1.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gQQMI1Vu5Q">
        .gQQMI1Vu5Q[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.9 - 9.551020408163266px);--minw-tablet:calc(100% / 3.9 - 8.923076923076923px);--minw-mobile:calc(100% / 1.9 - 11.368421052631579px);--maxw:calc(100% / 4.9 - 9.551020408163266px);--maxw-tablet:calc(100% / 3.9 - 8.923076923076923px);--maxw-mobile:calc(100% / 1.9 - 11.368421052631579px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gjS6pgUWNb gilv3LjYVp"
      data-index="4"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      
    <div
      
      data-id="gkzkPKEeWE"
      class="!gp-max-w-none gp-bg-black gp-overflow-hidden [&_>_article]:gp-aspect-[var(--aspect)] tablet:[&_>_article]:gp-aspect-[var(--aspect-tablet,_var(--aspect))] mobile:[&_>_article]:gp-aspect-[var(--aspect-mobile,_var(--aspect-tablet,_var(--aspect)))] gp-relative gkzkPKEeWE"
      style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#242424;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px;--aspect:auto"
    >
      
    <gp-lite-youtube-embed gp-data='{"lazy":true,"style":{"--aspect":"auto"},"videoTitle":"Video","iframeSrc":"https://www.youtube-nocookie.com/embed/mWfSDP36Vnc?autoplay=1&state=1&mute=1&&controls=1"}'>
      
      
      <article
        aria-hidden
        class="gp-relative gp-bg-cover gp-bg-center gp-lite-youtube-embed-article gp_lazybg"
        data-title="Video"
        style="--bgi:url('{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}');--aspect:auto"
      >
        
  <img
      id="gp-video-gkzkPKEeWE"
      
      draggable="false"
      class="gp-invisible gp-w-full gp-h-full gp_lazyforbg"
      data-src="{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}" data-srcset="{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}" src="{{ 'gempages_572751041980793671-73035fe3-7b16-4bda-bc73-97e394bfffb5.jpg' | file_url }}" width="2237" height="1678" alt="Poster"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      
    />
  
        <button
          type="button"
          class="gp-absolute gp-top-1/2 gp-left-1/2 gp-w-[68px] -gp-translate-x-1/2 -gp-translate-y-1/2 gp-lite-youtube-embed-button gp-visible gp-opacity-100"
          aria-label="Play"
        >
          <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
            <path
              d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
              fill="#f00"
            ></path>
            <path d="M 45,24 27,14 27,34" fill="#fff"></path>
          </svg>
        </button>
        
      </article>
    </gp-lite-youtube-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-youtube-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
  
      <style id="custom-css-gkzkPKEeWE">
        .gkzkPKEeWE[data-component-label="Video"] {

}
      </style>
    
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next gjS6pgUWNb-{{section.id}} gp-carousel-arrow-gjS6pgUWNb gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-relative  tablet:!gp-flex tablet:!gp-relative  mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#ECECEC;--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px"
  >
    <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817723121664360">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm29.66,109.66-40,40a8,8,0,0,1-11.32-11.32L140.69,128,106.34,93.66a8,8,0,0,1,11.32-11.32l40,40A8,8,0,0,1,157.66,133.66Z" /></svg>
    </div>
      <style>
    .gp-carousel-arrow-gjS6pgUWNb {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gjS6pgUWNb::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gjS6pgUWNb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gjS6pgUWNb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gjS6pgUWNb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gjS6pgUWNb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gjS6pgUWNb-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 7",
    "tag": "section",
    "class": "gps-575067858103436517 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/i0pixe-10/apps/gempages-cro/app/shopify/edit?pageType=GP_PRODUCT&editorId=575053851124565104&sectionId=575067858103436517)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
