{"article": [], "blog": [], "collection": [{"key": "custom_test_product", "namespace": "custom", "name": "custom.test_product", "description": "custom.test_product", "type": {"name": "product_reference", "category": "REFERENCE"}}], "company": [], "company_location": [], "location": [], "market": [], "order": [], "page": [], "product": [{"key": "color-pattern", "namespace": "shopify", "name": "Color", "description": "Defines the primary color or pattern, such as blue or striped", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "connection-type", "namespace": "shopify", "name": "Connection type", "description": "Specifies the type of connection used by electronics, such as wired or Bluetooth", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "compatible-filament", "namespace": "shopify", "name": "Compatible filament", "description": "Specifies the types of filament a 3D printer can use, like Polylactic acid (PLA) or nylon", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "enclosure-type", "namespace": "shopify", "name": "Enclosure type", "description": "Specifies the type of casing or cover for 3D printers, such as open frame or DIY enclosure", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "print-technology", "namespace": "shopify", "name": "Print technology", "description": "Specifies the process used by a printer, such as inkjet or laser", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "custom_product", "namespace": "mm-google-shopping", "name": "Google: Custom Product", "description": "Use to indicate whether or not the unique product identifiers (UPIs) GTIN, MPN, and brand are available for your product.", "type": {"name": "boolean", "category": "TRUE_FALSE"}}], "variant": [{"key": "custom_label_4", "namespace": "mm-google-shopping", "name": "Google: Custom Label 4", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_3", "namespace": "mm-google-shopping", "name": "Google: Custom Label 3", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_2", "namespace": "mm-google-shopping", "name": "Google: Custom Label 2", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_1", "namespace": "mm-google-shopping", "name": "Google: Custom Label 1", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_0", "namespace": "mm-google-shopping", "name": "Google: Custom Label 0", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_system", "namespace": "mm-google-shopping", "name": "Google: Size System", "description": "The country of the size system used by your product.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_type", "namespace": "mm-google-shopping", "name": "Google: Size Type", "description": "Your apparel product’s cut.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "mpn", "namespace": "mm-google-shopping", "name": "Google: MPN", "description": "Your product’s Manufacturer Part Number (MPN).", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "gender", "namespace": "mm-google-shopping", "name": "Google: Gender", "description": "The gender for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "condition", "namespace": "mm-google-shopping", "name": "Google: Condition", "description": "The condition of your product at time of sale.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "age_group", "namespace": "mm-google-shopping", "name": "Google: Age Group", "description": "The demographic for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "shop": []}