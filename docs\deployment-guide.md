# 部署和维护指南

## 概述

本指南详细说明Shopify Motion主题的部署流程、环境配置、版本管理和日常维护操作。

## 环境准备

### 1. 开发环境要求

#### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 14.0.0 或更高版本
- **Git**: 2.20.0 或更高版本
- **代码编辑器**: VS Code (推荐)

#### 必需工具安装
```bash
# 安装Node.js (使用nvm管理版本)
nvm install 16
nvm use 16

# 安装Shopify CLI
npm install -g @shopify/cli @shopify/theme

# 验证安装
shopify version
```

### 2. VS Code扩展推荐
```json
{
  "recommendations": [
    "shopify.theme-check-vscode",
    "shopify.shopify-liquid",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode"
  ]
}
```

## 项目初始化

### 1. 克隆项目
```bash
# 克隆仓库
git clone https://gitee.com/your-username/gitee_shopify_motion_augment_gitee.git
cd gitee_shopify_motion_augment_gitee

# 安装依赖（如果有package.json）
npm install
```

### 2. 配置Shopify连接
```bash
# 登录Shopify
shopify auth login

# 连接到开发商店
shopify theme dev --store=your-development-store.myshopify.com
```

### 3. 环境配置文件
创建 `.shopifyignore` 文件：
```
# 开发文件
*.md
docs/
.git/
.gitignore
.vscode/
node_modules/
package*.json

# 临时文件
*.tmp
*.log
.DS_Store
Thumbs.db
```

## 开发流程

### 1. 本地开发
```bash
# 启动开发服务器
shopify theme dev

# 指定特定商店
shopify theme dev --store=your-store.myshopify.com

# 实时预览
# 访问提供的预览URL，通常是 https://your-store.myshopify.com/?preview_theme_id=xxxxx
```

### 2. 代码同步
```bash
# 从线上拉取最新代码
shopify theme pull

# 推送本地更改到线上
shopify theme push

# 推送到特定主题
shopify theme push --theme=123456789
```

### 3. 分支管理策略
```bash
# 功能开发分支
git checkout -b feature/hero-section-enhancement
git add .
git commit -m "feat: 增强英雄区域动画效果"
git push origin feature/hero-section-enhancement

# 合并到主分支
git checkout main
git merge feature/hero-section-enhancement
git push origin main
```

## 部署流程

### 1. 测试环境部署

#### 创建测试主题
```bash
# 创建新主题用于测试
shopify theme push --unpublished --theme-name="Motion-Test-$(date +%Y%m%d)"
```

#### 自动化测试脚本
```bash
#!/bin/bash
# deploy-test.sh

echo "开始测试环境部署..."

# 检查代码质量
npm run lint
if [ $? -ne 0 ]; then
  echo "代码质量检查失败"
  exit 1
fi

# 推送到测试主题
shopify theme push --unpublished --theme-name="Motion-Test-$(date +%Y%m%d-%H%M)"

echo "测试环境部署完成"
```

### 2. 生产环境部署

#### 预发布检查清单
- [ ] 代码已通过所有测试
- [ ] 在测试环境验证功能正常
- [ ] 备份当前生产主题
- [ ] 确认部署时间窗口
- [ ] 通知相关团队成员

#### 生产部署脚本
```bash
#!/bin/bash
# deploy-production.sh

echo "开始生产环境部署..."

# 确认部署
read -p "确认要部署到生产环境吗? (y/N): " confirm
if [[ $confirm != [yY] ]]; then
  echo "部署已取消"
  exit 0
fi

# 备份当前主题
echo "备份当前主题..."
shopify theme pull --live

# 创建备份分支
git checkout -b backup/production-$(date +%Y%m%d-%H%M)
git add .
git commit -m "backup: 生产环境备份 $(date)"
git push origin backup/production-$(date +%Y%m%d-%H%M)
git checkout main

# 部署到生产
echo "部署到生产环境..."
shopify theme push --live

echo "生产环境部署完成"
```

### 3. 回滚策略
```bash
#!/bin/bash
# rollback.sh

echo "开始回滚操作..."

# 列出可用的备份
git branch -r | grep backup/production

read -p "请输入要回滚的备份分支名: " backup_branch

# 切换到备份分支
git checkout $backup_branch

# 推送到生产
shopify theme push --live

echo "回滚完成"
```

## 版本管理

### 1. 语义化版本控制
```bash
# 主版本更新 (破坏性更改)
git tag -a v2.0.0 -m "主要版本更新: 重构动画系统"

# 次版本更新 (新功能)
git tag -a v1.1.0 -m "次版本更新: 添加粒子效果"

# 补丁版本 (bug修复)
git tag -a v1.0.1 -m "补丁更新: 修复移动端动画问题"

# 推送标签
git push origin --tags
```

### 2. 变更日志维护
创建 `CHANGELOG.md` 文件：
```markdown
# 变更日志

## [1.1.0] - 2025-07-07

### 新增
- IR3产品页面粒子效果动画
- 磁性按钮交互功能
- 响应式动画适配

### 改进
- 优化滚动性能
- 增强移动端体验
- 更新文档结构

### 修复
- 修复Safari浏览器兼容性问题
- 解决动画延迟计算错误

## [1.0.0] - 2025-07-01

### 新增
- 初始版本发布
- 基础动画系统
- IR3英雄区域组件
```

## 性能监控

### 1. 性能指标监控
```javascript
// 性能监控脚本
function initPerformanceMonitoring() {
  // 页面加载时间
  window.addEventListener('load', function() {
    const loadTime = performance.now();
    console.log(`页面加载时间: ${loadTime.toFixed(2)}ms`);
    
    // 发送到分析服务
    if (typeof gtag !== 'undefined') {
      gtag('event', 'timing_complete', {
        name: 'load',
        value: Math.round(loadTime)
      });
    }
  });

  // 动画性能监控
  const animationObserver = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.duration > 16) { // 超过16ms可能影响60fps
        console.warn(`动画性能警告: ${entry.name} 耗时 ${entry.duration}ms`);
      }
    });
  });
  
  animationObserver.observe({entryTypes: ['measure']});
}
```

### 2. 错误监控
```javascript
// 错误收集和报告
window.addEventListener('error', function(e) {
  const errorData = {
    message: e.message,
    filename: e.filename,
    lineno: e.lineno,
    colno: e.colno,
    stack: e.error ? e.error.stack : null,
    userAgent: navigator.userAgent,
    url: window.location.href,
    timestamp: new Date().toISOString()
  };
  
  // 发送错误报告
  sendErrorReport(errorData);
});

function sendErrorReport(errorData) {
  // 发送到错误监控服务
  fetch('/api/errors', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(errorData)
  }).catch(err => {
    console.error('错误报告发送失败:', err);
  });
}
```

## 日常维护

### 1. 定期检查清单

#### 每周检查
- [ ] 检查主题性能指标
- [ ] 审查错误日志
- [ ] 验证关键功能正常
- [ ] 检查浏览器兼容性

#### 每月检查
- [ ] 更新依赖包版本
- [ ] 审查代码质量
- [ ] 优化图片资源
- [ ] 检查SEO指标

#### 每季度检查
- [ ] 全面性能审计
- [ ] 安全性检查
- [ ] 用户体验评估
- [ ] 技术债务清理

### 2. 自动化维护脚本
```bash
#!/bin/bash
# maintenance.sh

echo "开始日常维护检查..."

# 检查主题文件完整性
echo "检查文件完整性..."
find . -name "*.liquid" -exec liquid-syntax-check {} \;

# 检查CSS语法
echo "检查CSS语法..."
find ./assets -name "*.css" -exec csslint {} \;

# 检查JavaScript语法
echo "检查JavaScript语法..."
find ./assets -name "*.js" -exec jshint {} \;

# 优化图片
echo "优化图片资源..."
find ./assets -name "*.png" -exec optipng {} \;
find ./assets -name "*.jpg" -exec jpegoptim {} \;

echo "维护检查完成"
```

### 3. 备份策略
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份主题文件
echo "备份主题文件..."
tar -czf $BACKUP_DIR/theme-files.tar.gz \
  --exclude=node_modules \
  --exclude=.git \
  .

# 备份主题设置
echo "备份主题设置..."
shopify theme pull --only=config/settings_data.json
cp config/settings_data.json $BACKUP_DIR/

echo "备份完成: $BACKUP_DIR"
```

## 故障排除

### 1. 常见问题解决

#### 主题推送失败
```bash
# 检查网络连接
ping shopify.com

# 重新认证
shopify auth logout
shopify auth login

# 强制推送
shopify theme push --force
```

#### 动画不工作
```javascript
// 检查浏览器支持
if (!window.IntersectionObserver) {
  console.error('浏览器不支持IntersectionObserver');
  // 加载polyfill
}

// 检查CSS加载
const cssLoaded = document.querySelector('link[href*="theme.css"]');
if (!cssLoaded) {
  console.error('主题CSS未加载');
}
```

#### 性能问题诊断
```bash
# 使用Lighthouse进行性能审计
npm install -g lighthouse
lighthouse https://your-store.myshopify.com --output=html --output-path=./performance-report.html

# 分析包大小
npm install -g webpack-bundle-analyzer
# 如果使用webpack构建
```

### 2. 紧急响应流程

#### 生产环境故障
1. **立即评估影响范围**
2. **启动回滚程序**
3. **通知相关人员**
4. **记录故障详情**
5. **修复根本原因**
6. **更新文档和流程**

#### 联系信息
- **技术负责人**: [联系方式]
- **Shopify支持**: https://help.shopify.com
- **紧急联系**: [紧急联系方式]

## 安全最佳实践

### 1. 代码安全
- 定期更新依赖包
- 避免在代码中硬编码敏感信息
- 使用HTTPS连接
- 验证用户输入

### 2. 访问控制
- 限制主题编辑权限
- 使用强密码策略
- 启用两步验证
- 定期审查访问权限

---

*部署指南版本: v1.0 | 最后更新: 2025-07-07*
