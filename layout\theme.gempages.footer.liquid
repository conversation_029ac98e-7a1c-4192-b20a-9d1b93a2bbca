<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}" dir="{{ settings.text_direction }}">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.color_button }}">
  <link rel="canonical" href="{{ canonical_url }}">
  <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
  <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
  <link rel="dns-prefetch" href="https://productreviews.shopifycdn.com">
  <link rel="dns-prefetch" href="https://ajax.googleapis.com">
  <link rel="dns-prefetch" href="https://maps.googleapis.com">
  <link rel="dns-prefetch" href="https://maps.gstatic.com">

  {%- if settings.favicon != blank -%}
    <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png" />
  {%- endif -%}

  {%- render 'seo-title' -%}

  {%- if page_description -%}
  <meta name="description" content="{{ page_description | escape }}">
  {%- endif -%}

  {%- render 'social-meta-tags' -%}

  {%- render 'font-face' -%}
  {{ 'theme.css' | asset_url | stylesheet_tag: preload: true }}
  {%- render 'css-variables' -%}

  <script>
    document.documentElement.className = document.documentElement.className.replace('no-js', 'js');

    window.theme = window.theme || {};
    theme.routes = {
      home: "{{ routes.root_url }}",
      cart: "{{ routes.cart_url | append: '.js' }}",
      cartPage: "{{ routes.cart_url }}",
      cartAdd: "{{ routes.cart_add_url | append: '.js' }}",
      cartChange: "{{ routes.cart_change_url | append: '.js' }}",
      search: "{{ routes.search_url }}",
      predictiveSearch: "{{ routes.predictive_search_url }}"
    };
    theme.strings = {
      soldOut: {{ 'products.product.sold_out' | t | json }},
      unavailable: {{ 'products.product.unavailable' | t | json }},
      inStockLabel: {{ 'products.product.in_stock_label' | t | json }},
      oneStockLabel: {{ 'products.product.stock_label.one' | t: count: '[count]' | json }},
      otherStockLabel: {{ 'products.product.stock_label.other' | t: count: '[count]' | json }},
      willNotShipUntil: {{ 'products.product.will_not_ship_until' | t: date: '[date]' | json }},
      willBeInStockAfter: {{ 'products.product.will_be_in_stock_after' | t: date: '[date]' | json }},
      waitingForStock: {{ 'products.product.waiting_for_stock' | t | json }},
      cartSavings: {{ 'cart.general.savings_html' | t: savings: '[savings]' | json }},
      cartEmpty: {{ 'cart.general.empty' | t | json }},
      cartTermsConfirmation: {{ 'cart.general.terms_confirm' | t | json }},
      searchCollections: {{ 'general.search.collections' | t | json }},
      searchPages: {{ 'general.search.pages' | t | json }},
      searchArticles: {{ 'general.search.articles' | t | json }},
      maxQuantity: {{ 'cart.general.max_quantity' | t: quantity: '[quantity]', title: '[title]' | json }}
    };
    theme.settings = {
      cartType: {{ settings.cart_type | json }},
      isCustomerTemplate: {% if request.page_type contains 'customers/' %}true{% else %}false{% endif %},
      moneyFormat: {{ shop.money_format | json }},
      predictiveSearch: {{ settings.predictive_search_enabled }},
      predictiveSearchType: {{ settings.search_type | json }},
      quickView: {{ settings.quick_shop_enable }},
      themeName: 'Motion',
      themeVersion: "11.0.0"
    };
  </script>

  {{ content_for_header }}

  <script src="{{ 'vendor-scripts-v14.js' | asset_url | split: '?' | first }}" defer="defer"></script>

  {%- if shop.enabled_currencies.size > 1 -%}
    <link rel="stylesheet" href="{{ 'country-flags.css' | asset_url | split: '?' | first }}">
  {%- endif -%}

  <script src="{{ 'theme.js' | asset_url }}" defer="defer"></script>

  {%- if request.page_type contains 'customers/' -%}
    <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer="defer"></script>
  {%- endif -%}

  {% if request.design_mode %}
    <script>theme.settings.email = {{ shop.email | json }}</script>
    <script src="https://api.archetypethemes.co/design-mode.js" defer="defer"></script>
  {% endif %}
</head>

<body class="template-{{ template | replace: '.', ' ' | truncatewords: 1, '' | handle }}{% if request.path == '/challenge' %} template-challange{% endif %}" data-transitions="{{ settings.animate_page_transitions }}" data-type_header_capitalize="{{ settings.type_header_capitalize }}" data-type_base_accent_transform="{{ settings.type_base_accent_transform }}" data-type_header_accent_transform="{{ settings.type_header_accent_transform }}" data-animate_sections="{{ settings.animate_sections }}" data-animate_underlines="{{ settings.animate_underlines }}" data-animate_buttons="{{ settings.animate_buttons }}" data-animate_images="{{ settings.animate_images }}" data-animate_page_transition_style="{{ settings.animate_page_transition_style }}" data-type_header_text_alignment="{{ settings.type_headers_align_text }}" data-animate_images_style="{{ settings.animate_images_style | default: "zoom-fade" }}">

  {% if settings.animate_page_transitions %}
    <script type="text/javascript">window.setTimeout(function() { document.body.className += " loaded"; }, 25);</script>
  {% endif %}

  <a class="in-page-link visually-hidden skip-link" href="#MainContent">{{ 'general.accessibility.skip_to_content' | t }}</a>

  <div id="PageContainer" class="page-container">
    <div class="transition-body">

      <div style="display:none"> {%- sections 'header-group' -%} </div>
      <div style="display:none"> {%- sections 'popup-group' -%} </div>

      <main class="main-content" id="MainContent">
        {{ content_for_layout }}
      </main>

      {%- sections 'footer-group' -%}

    </div>
  </div>

  {%- liquid
    render 'video-modal'
    render 'photoswipe-template'
    render 'tool-tip'
  -%}

  
  <template id="predictiveImageMarkup">
    <image-element>
      <img width height src loading="lazy" sizes="(min-width: 769px) 100px, 60px">
    </image-element>
  </template>
</body>
</html>
